package com.nymbl.config.service;

import com.nymbl.database.BatchDataSourceProvider;

import com.google.common.base.Strings;
import javax.sql.DataSource;
import com.nymbl.audit.AuditMap;
import com.nymbl.config.Constants;
import com.nymbl.config.security.SecurityUtils;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.NumberUtil;
import com.nymbl.config.utils.OptimisticLockingUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.master.repository.CompanyRepository;
import com.nymbl.master.repository.UserRepository;
import com.nymbl.tenant.MultiTenantConnectionProviderImpl;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.interfaces.LockedEntity;
import com.nymbl.tenant.model.*;
import io.sentry.Sentry;
import jakarta.persistence.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;

import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.NotAudited;
import org.hibernate.envers.RevisionType;
import org.hibernate.envers.query.AuditEntity;
import org.hibernate.envers.query.AuditQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.nymbl.config.utils.OptimisticLockingUtil.*;

/**
 * Created by Bradley Moore on 05/20/2017.
 */
@Slf4j
public abstract class AbstractTableService<T, ID extends Serializable> {

    protected final JpaRepository<T, ID> repository;
    private final JpaSpecificationExecutor<T> specificationExecutor;

    @Autowired
    private MultiTenantConnectionProviderImpl multiTenantConnectionProvider;

    @Autowired
    private OptimisticLockingUtil optimisticLockingUtil;

    @Autowired
    private UserRepository userRepository;

    @PersistenceContext(name = "tenantEntityManager", unitName = "tenant")
    @Qualifier("tenantEntityManager")
    private EntityManager tenantEntityManager;

    @PersistenceContext(name = "masterEntityManager", unitName = "master")
    @Qualifier("masterEntityManager")
    private EntityManager masterEntityManager;

    @SuppressWarnings(value = "unchecked")
    public AbstractTableService(JpaRepository<T, ID> repository) {
        this.repository = repository;
        this.specificationExecutor = (JpaSpecificationExecutor<T>) repository;
    }

    public T save(T object) {
        T result = null;
        if (!TableObjectContainer.isApplicationRunning()) {
            if (object instanceof ModelStub && ((ModelStub) object).getId() == null) {
                ((ModelStub) object).setId(1L);
            } else if (object instanceof DetailedWrittenOrder && ((DetailedWrittenOrder) object).getId() == null) {
                ((DetailedWrittenOrder) object).setId(1L);
            } else if (object instanceof FinancialResponsibility && ((FinancialResponsibility) object).getId() == null) {
                ((FinancialResponsibility) object).setId(1L);
            } else if (object instanceof Prescription && ((Prescription) object).getId() == null) {
                ((Prescription) object).setId(1L);
            }
            result = object;
        } else {
            // Prescriptions rely on the exception for Optimistic Locking
            if (object instanceof Prescription) {
                result = repository.save(object);
            } else {
                // TODO: Remove this stuff if safe to do so
                try {
                    result = repository.save(object);
                } catch (Exception e) {
                    String error = StringUtil.getExceptionAsString(e);
                    String userName = SecurityUtils.getCurrentLogin();
                    String message = "Error saving object on tenant = " + TenantContext.getCurrentTenant() + ", for username = " + userName + " of class = " + object.getClass().getName();
                    if (error.contains("Unknown column ")) {
                        String field = error.substring(error.indexOf("Unknown column ") + "Unknown column '".length());
                        field = field.substring(0, field.indexOf("'"));
                        String table = object.getClass().getName();
                        table = table.substring(table.lastIndexOf(".") + 1).toLowerCase();
                        message += "\n\n#######################\nDid you add this field, " + field + ", to the " + table + "_audit table?\n#######################\n\n";
                    }
                    message += "With error = " + e.getMessage();
                    log.error(message);
                    throw e;
                }
            }
        }
        loadForeignKeys(result);
        TableObjectContainer.storeForJUnitTestingIfApplicationIsNotRunning(result);
        return result;
    }

    public List<T> save(List<T> list) {
        List<T> results = null;
        if (!TableObjectContainer.isApplicationRunning()) {
            results = list;
        } else {
            results = repository.saveAll(list);
        }
        loadForeignKeysList(results);
        TableObjectContainer.storeForJUnitTestingIfApplicationIsNotRunning(list);
        return results;
    }

    public void delete(ID key) {
        if (!TableObjectContainer.isApplicationRunning()) {
            String className = this.getClass().getName();
            className = className.substring(className.lastIndexOf("."));
            String modelKey = "com.nymbl.tenant.model";
            if (className.endsWith("Service")) {
                className = className.substring(0, className.indexOf("Service"));
            } else if (className.endsWith("Repository")) {
                className = className.substring(0, className.indexOf("Repository"));
            }
            modelKey = modelKey + className;
            if (TableObjectContainer.getTableMap().containsKey(modelKey)) {
                List<Object> modelList = ((List<Object>) TableObjectContainer.getTableMap().get(modelKey));
                modelList.removeIf(object -> (object instanceof ModelStub && ((ModelStub) object).getId() != null && ((ModelStub) object).getId() == key) ||
                        (object instanceof DetailedWrittenOrder && ((DetailedWrittenOrder) object).getId() != null && ((DetailedWrittenOrder) object).getId() != key) ||
                        (object instanceof FinancialResponsibility && ((FinancialResponsibility) object).getId() != null && ((FinancialResponsibility) object).getId() == key) ||
                        (object instanceof Prescription && ((Prescription) object).getId() != null && ((Prescription) object).getId() == key));
            }
        } else {
            repository.deleteById(key);
        }
    }

    public void deleteInBatch(List<T> list) {
        // delete in batch uses a query to delete, which means no audits
        // SCRUM-2148: pre-req is to switch to deleteAll so that we track deletes in audit
        repository.deleteAll(list);
    }

    public T findOne(ID id) {
        if (id == null) {
            return null;
        }
        Optional<T> optionalT = repository.findById(id);
        if (optionalT.isPresent()) {
            T result = optionalT.get();
            loadForeignKeys(result);
            return result;
        } else {
            return null;
        }
    }

    public List<T> findAll() {
        List<T> results = repository.findAll();
        loadForeignKeysList(results);
        return results;
    }

    protected void loadForeignKeysList(List<T> results) {
        for (T o : results) {
            loadForeignKeys(o);
        }
    }

    public List<T> findAll(Specification<T> spec) {
        List<T> results = specificationExecutor.findAll(spec);
        loadForeignKeysList(results);
        return results;
    }

    public List<T> findAll(Specification<T> spec, Sort sort) {
        List<T> results = specificationExecutor.findAll(spec, sort);
        loadForeignKeysList(results);
        return results;
    }

    public List<T> findAll(Specification<T> spec, Pageable pageable) {
        List<T> results = specificationExecutor.findAll(spec, pageable).getContent();
        loadForeignKeysList(results);
        return results;
    }

    public List<T> findAll(Example<T> example) {
        List<T> results = repository.findAll(example);
        loadForeignKeysList(results);
        return results;
    }

    public List<T> findAll(Example<T> example, Pageable pageable) {
        List<T> results = repository.findAll(example, pageable).getContent();
        loadForeignKeysList(results);
        return results;
    }

    public List<T> findAll(Sort sort) {
        List<T> results = repository.findAll(sort);
        loadForeignKeysList(results);
        return results;
    }

    public List<T> findAll(Pageable pageable) {
        List<T> results = repository.findAll(pageable).getContent();
        loadForeignKeysList(results);
        return results;
    }

    public Page<T> findAllPage(Pageable pageable) {
        Page<T> results = repository.findAll(pageable);
        loadForeignKeysList(results.getContent());
        return results;
    }

    public List<T> findAllById(Iterable<ID> ids) {
        return repository.findAllById(ids);
    }

    public Pageable makePageable(Sort.Direction direction, String columnName, int startPage, int pageSize) {
        if (direction != null && !StringUtil.isBlank(columnName))
            return PageRequest.of(startPage, pageSize, makeSort(direction, columnName));
        return PageRequest.of(startPage, pageSize);
    }

    public Pageable makePageable(Sort sort, int startPage, int pageSize) {
        return PageRequest.of(startPage, pageSize, sort);
    }

    protected Sort makeSort(Sort.Direction direction, String columnName) {
        return Sort.by(new Sort.Order(direction, columnName).ignoreCase());
    }

    public Boolean compare(BigDecimal left, String operator, BigDecimal right) {
        return NumberUtil.compare(left, operator, right);
    }

    public Payment initializePayment(AutoPostPatient app, Long createById) {
        Payment p = new Payment();
        String paymentMethod = app.getAutoPost().getPaymentMethod();
        if ("ACH".equals(paymentMethod) || "FWT".equals(paymentMethod)) {
            if (app.getAutoPost().getPayerId() != null) {
                p.setPaymentType("insurance_payment_electronic");
                p.setPayerType("insurance_company");
            } else {
                p.setPaymentType("patient_electronic");
                p.setPayerType("patient");
            }
        } else if ("CHK".equals(paymentMethod)) {
            p.setPaymentType("insurance_payment_check");
            p.setPayerType("insurance_company");
        } else if ("CCD".equals(paymentMethod)) {
            if (app.getAutoPost().getPayerId() != null && app.getAutoPost().getInsuranceCompany() != null && !app.getAutoPost().getInsuranceCompany().getName().equals("Self Pay")) {
                p.setPaymentType("insurance_payment_credit_card");
                p.setPayerType("insurance_company");
            } else {
                p.setPaymentType("patient_credit_card");
                p.setPayerType("patient");
            }
        } else if ("ECK".equals(paymentMethod)) {
            p.setPaymentType("patient_electronic");
            p.setPayerType("patient");
        } else if ("CSH".equals(paymentMethod)) {
            p.setPaymentType("cash");
            p.setPayerType("patient");
        } else if ("PCK".equals(paymentMethod)) {
            p.setPaymentType("patient_check");
            p.setPayerType("patient");
        } else {
            p.setPaymentType("autopost");
            p.setPayerType("insurance_company");
        }
        p.setInsuranceCompanyId(app.getAutoPost().getPayerId());
        p.setDepositDate(app.getAutoPost().getCheckDate());
        p.setCheckNumber(app.getAutoPost().getCheckNumber());
        p.setIcn(app.getIcn());
        p.setCredit(BigDecimal.ZERO);
        p.setDescription(app.getTransactionMessage() != null ? app.getTransactionMessage() : "");
        p.setCreatedById(createById);
        p.setCreatedAt(new Timestamp(System.currentTimeMillis()));
        return p;
    }

    public List<T> getResults(Specification<T> spec) {
        Pageable pageable = makePageable(Sort.Direction.ASC, "lastName", 0, 1000);
        Page<T> result = null;
        if (spec != null)
            result = specificationExecutor.findAll(spec, pageable);
        else
            result = repository.findAll(pageable);
        return result.getContent();
    }


    /**
     * Example call prescriptionLCodeService.compareAuditsFromParent(11L, "prescriptionId");
     *
     * @param parentId   - prescriptionId (prescription_l_code.prescription_id)
     * @param columnName (Java property name in Prescription_L_Code.java)
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> compareAuditsFromParent(ID parentId, String columnName) {
        List<Map<String, Object>> results = new ArrayList<>();
        try {
            Type type = getClass().getGenericSuperclass();
            Class<T> classType = (Class<T>) ((ParameterizedType) type).getActualTypeArguments()[0];
            EntityManager em;
            boolean masterAudit = true;
            if (type.getClass().getPackage().getName().equals("com.nymbl.master.model")) {
                em = masterEntityManager.getEntityManagerFactory().createEntityManager();
            } else {
                em = tenantEntityManager.getEntityManagerFactory().createEntityManager();
                masterAudit = false;
            }
            AuditReader reader = AuditReaderFactory.get(em);
            List<Object[]> revisions = (List<Object[]>) reader.createQuery()
                    .forRevisionsOfEntity(classType, false, true)
                    .add(AuditEntity.property(columnName).eq(parentId))
                    .getResultList();
            for (Object[] o : revisions) {
                T clazz = (T) o[0];

                long revisionId = ((AuditRevision) o[1]).getRevisionId();
                String revisionType = "MOD"; //ADD, MOD and DEL)
                if (o.length >= 3) {
                    revisionType = ((RevisionType) o[2]).name();
                }

                if (revisionType.equals("ADD") || revisionType.equals("DEL")) {
                    for (Field currentField : clazz.getClass().getDeclaredFields()) {
                        currentField.setAccessible(true); // if you want to modify private fields
                        Map<String, Object> map = new HashMap<>();

                        String sql = AuditMap.map.get(clazz.getClass().getSimpleName().concat(".").concat(currentField.getName()));
                        if (!StringUtil.isBlank(sql)) {
                            List<?> currentList;
                            String cSQL = MessageFormat.format(sql, currentField.get(clazz) != null ? currentField.get(clazz).toString().replace(",", "") : null);
                            if (sql.startsWith("SELECT o FROM User o")) {
                                currentList = masterEntityManager.createQuery(cSQL).getResultList();
                            } else {
                                currentList = tenantEntityManager.createQuery(cSQL).getResultList();
                            }
                            if (currentList.size() == 1) {
                                if ((currentList.get(0) instanceof L_Code)) { // can expand to other class types or switch out to a function per list


                                    map.put("field", "HCPCS Selection"); // currentField.getName());

                                    if (revisionType.equals("ADD")) {
                                        map.put("currentValue", currentList.get(0).toString());
                                        map.put("previousValue", "--CREATED--");
                                    } else {
                                        map.put("previousValue", currentList.get(0).toString());
                                        map.put("currentValue", "--DELETED--");
                                    }

                                    Date revisionDate = reader.getRevisionDate(revisionId);
                                    map.put("revisionDate", revisionDate.toString());
                                    String username;
                                    String ipAddress = "";
                                    if (masterAudit) {
                                        username = reader.findRevision(com.nymbl.master.model.AuditRevision.class, revisionId).getUser();
                                        ipAddress = reader.findRevision(com.nymbl.master.model.AuditRevision.class, revisionId).getIpAddress();
                                    } else {
                                        username = reader.findRevision(com.nymbl.tenant.model.AuditRevision.class, revisionId).getUser();
                                        ipAddress = reader.findRevision(com.nymbl.tenant.model.AuditRevision.class, revisionId).getIpAddress();
                                    }
                                    map.put("username", username);
                                    map.put("revisionId", revisionId);
                                    map.put("ipAddress", ipAddress);

                                    results.add(map);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            em.close();
        } catch (Exception e) {
            //em.close(); leaks...
            //e.printStackTrace();
            log.error(StringUtil.getExceptionAsString(e));
            Sentry.captureException(e);
        }
        return results;
    }


    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> compareAudits(ID id) {
        List<Map<String, Object>> results = new ArrayList<>();
        try {
            T main = findOne(id);
            EntityManager em;
            boolean masterAudit = true;
            if (main.getClass().getPackage().getName().equals("com.nymbl.master.model")) {
                em = masterEntityManager.getEntityManagerFactory().createEntityManager();
            } else {
                em = tenantEntityManager.getEntityManagerFactory().createEntityManager();
                masterAudit = false;
            }
            AuditReader reader = AuditReaderFactory.get(em);
            List<Number> revisions = reader.getRevisions(main.getClass(), id);
            if(revisions.size() <= 0) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", 1);
                //map.put("field", "--");
                //map.put("revisionDate", "--");
                map.put("username", "--");
                map.put("revisionId", 0);
                map.put("currentValue", "--No audit history found--");
                map.put("previousValue", "--No audit history found--");
                results.add(map);
            }
            T previous = null;
            Number previousNumber = null;
            for (Number rev : revisions) {
                T current = (T) reader.find(main.getClass(), id, rev);
                if (previousNumber != null) {
                    for (Field currentField : current.getClass().getDeclaredFields()) {
                        Field previousField = previous.getClass().getDeclaredField(currentField.getName());
                        boolean equals = true;
                        if (!currentField.isAnnotationPresent(NotAudited.class)) {
                            currentField.setAccessible(true); // if you want to modify private fields
                            previousField.setAccessible(true); // if you want to modify private fields
                            Object d1 = previousField.get(previous);
                            Object d2 = currentField.get(current);
                            if (d1 != null && d2 != null) {
                                String currentFieldType = currentField.getType().getName();
                                if (currentFieldType.equals("java.sql.Date") || currentFieldType.equals("java.sql.Time") || currentFieldType.equals("java.sql.Timestamp") || currentFieldType.equals("java.time.OffsetDateTime")) {
                                    equals = previousField.get(previous).equals(currentField.get(current));
                                } else if (currentFieldType.equals("java.math.BigDecimal")) {
                                    equals = ((BigDecimal) currentField.get(current)).compareTo((BigDecimal) previousField.get(previous)) == 0;
                                } else if (currentFieldType.equals("java.lang.Long")) {
                                    equals = ((Long) currentField.get(current)).compareTo((Long) previousField.get(previous)) == 0;
                                } else if (currentFieldType.equals("java.lang.Boolean")) {
                                    equals = ((Boolean) currentField.get(current)).compareTo((Boolean) previousField.get(previous)) == 0;
                                } else if (currentFieldType.contains("com.nymbl.config.enums")) {
                                    equals = previousField.get(previous).equals(currentField.get(current));
                                } else {
                                    equals = EqualsBuilder.reflectionEquals(previousField.get(previous), currentField.get(current), false);
                                }
                            } else {
                                equals = d1 == null && d2 == null;
                            }
                        }
                        if (!equals) {
                            Map<String, Object> map = new HashMap<>();
                            Date revisionDate = reader.getRevisionDate(rev);
                            String username;
                            String ipAddress = "";
                            if (masterAudit) {
                                username = reader.findRevision(com.nymbl.master.model.AuditRevision.class, rev).getUser();
                                ipAddress = reader.findRevision(com.nymbl.master.model.AuditRevision.class, rev).getIpAddress();
                            } else {
                                ipAddress = reader.findRevision(com.nymbl.tenant.model.AuditRevision.class, rev).getIpAddress();
                                username = reader.findRevision(com.nymbl.tenant.model.AuditRevision.class, rev).getUser();
                            }
                            String sql = AuditMap.map.get(current.getClass().getSimpleName().concat(".").concat(currentField.getName()));
                            map.put("id", id);
                            if (!StringUtil.isBlank(sql)) {
                                List<?> currentList;
                                List<?> previousList;
                                String cSQL = MessageFormat.format(sql, currentField.get(current) != null ? currentField.get(current).toString().replace(",", "") : null);
                                String pSQL = MessageFormat.format(sql, previousField.get(previous) != null ? previousField.get(previous).toString().replace(",", "") : null);
                                if (sql.startsWith("SELECT o FROM User o") || sql.startsWith("SELECT o FROM ClearingHousePayer o")) {
                                    currentList = masterEntityManager.createQuery(cSQL).getResultList();
                                    previousList = masterEntityManager.createQuery(pSQL).getResultList();
                                } else {
                                    currentList = tenantEntityManager.createQuery(cSQL).getResultList();
                                    previousList = tenantEntityManager.createQuery(pSQL).getResultList();
                                }
                                map.put("field", currentField.getName().replaceAll("Id$", ""));
                                if (currentList.size() == 1) {
                                    map.put("currentValue", currentList.get(0).toString());
                                } else {
                                    map.put("currentValue", null);
                                }
                                if (previousList.size() == 1) {
                                    map.put("previousValue", previousList.get(0).toString());
                                } else {
                                    map.put("previousValue", null);
                                }
                            } else {
                                map.put("field", currentField.getName());
                                map.put("previousValue", resolveValue(previousField, previous));
                                map.put("currentValue", resolveValue(currentField, current));
                            }
                            map.put("revisionDate", revisionDate.toString());
                            map.put("username", username);
                            map.put("revisionId", rev);
                            map.put("ipAddress", ipAddress);

                            results.add(map);
                        }
                    }
                } else {
                    // see if we have a create ("ADD") record or if this object predates the audit history...
                    Object[] oFirstRevision = (Object[]) reader.createQuery()
                            .forRevisionsOfEntity(main.getClass(), false, true)
                            .add(AuditEntity.id().eq(id))
                            .add(AuditEntity.revisionNumber().eq(rev)).getSingleResult();

                    Map<String, Object> map = new HashMap<>();
                    Field[] fields = current.getClass().getDeclaredFields();
                    map.put("id", "Audit Start");
                    map.put("field", fields[0].getName());
                    Date revisionDate = reader.getRevisionDate(rev);
                    map.put("revisionDate", revisionDate.toString());
                    String username;
                    String ipAddress = "";
                    if (masterAudit) {
                        username = reader.findRevision(com.nymbl.master.model.AuditRevision.class, rev).getUser();
                        ipAddress = reader.findRevision(com.nymbl.master.model.AuditRevision.class, rev).getIpAddress();
                    } else {
                        ipAddress = reader.findRevision(com.nymbl.tenant.model.AuditRevision.class, rev).getIpAddress();
                        username = reader.findRevision(com.nymbl.tenant.model.AuditRevision.class, rev).getUser();
                    }
                    map.put("username", username);
                    map.put("revisionId", rev);
                    map.put("ipAddress", ipAddress);
                    if (oFirstRevision.length >= 3 && ((RevisionType) oFirstRevision[2]).name().equals("ADD")) {
                        map.put("currentValue", "--CREATED--");
                        map.put("previousValue", "--CREATED--");

                    } else {
                        map.put("currentValue", "--created prior to 8/2020--");
                        map.put("previousValue", "--created prior to 8/2020--");
                    }
                    results.add(map);
                }
                previousNumber = rev;
                previous = current;
            }
            em.close();
        } catch (Exception e) {
            //em.close(); leaks...
            //e.printStackTrace();
            log.error(StringUtil.getExceptionAsString(e));
            Sentry.captureException(e);
        }
        return results;
    }

    public Map<String, Object> getAuditDetails(ID id, Map<String, Object> respMap) {
        List<LockedEntity> diffs = (List<LockedEntity>) respMap.get(DIFFS);

        try {
            T main = findOne(id);
            EntityManager em = tenantEntityManager.getEntityManagerFactory().createEntityManager();

            diffs = diffs.stream().map(entity -> {

                boolean isIdField = entity.getPropertyName().endsWith("Id");

                AuditReader reader = AuditReaderFactory.get(em);
                AuditQuery query = reader.createQuery().forRevisionsOfEntity(main.getClass(), false, false);
                query.addProjection(AuditEntity.revisionNumber());
                query.add(AuditEntity.id().eq(id));
                query.add(AuditEntity.revisionNumber().maximize());

                List<Number> results = query.getResultList();
                String username = reader.findRevision(com.nymbl.tenant.model.AuditRevision.class, results.get(0)).getUser();
                Date revisionDate = reader.getRevisionDate(results.get(0));

                entity.setUpdatedOn(revisionDate.toString());
                entity.setUpdatedBy(username);

                if (isIdField) {
                    entity.setFieldName(entity.getFieldName().replaceAll("Id$", ""));
                    entity.setCurrentValue(getIdObject(entity.getPropertyName(), main.getClass().getSimpleName(), entity.getCurrentValue()));
                    entity.setChangedValue(getIdObject(entity.getPropertyName(), main.getClass().getSimpleName(), entity.getChangedValue()));
                }

                return entity;
            }).collect(Collectors.toList());
        } catch (Exception ex) {
            log.error(StringUtil.getExceptionAsString(ex));
            Sentry.captureException(ex);
        }
        respMap.put(DIFFS, diffs);
        return respMap;
    }

    public String getIdObject(String propertyName, String className, String id) {
        String sql = AuditMap.map.get(className.concat(".").concat(propertyName));

        if (StringUtils.isNotBlank(id) && !id.equalsIgnoreCase("NA"))
        {
            List idObjectList;
            if (StringUtils.isNotBlank(sql)) {
                String cSQL = MessageFormat.format(sql, id);
                if (sql.startsWith("SELECT o FROM User o") || sql.startsWith("SELECT o FROM WaystarPayer o")) {
                    idObjectList = masterEntityManager.createQuery(cSQL).getResultList();
                } else {
                    idObjectList = tenantEntityManager.createQuery(cSQL).getResultList();
                }

                if (!idObjectList.isEmpty()) {
                    return idObjectList.get(0).toString();
                }
            }
        }
        return id;
    }


    private Object resolveValue(Field field, Object o) throws IllegalAccessException {
        Object t = field.get(o);
        if (t instanceof Boolean) {
            if ((Boolean) t) {
                return "Yes";
            } else {
                return "No";
            }
        } else if (t instanceof Timestamp) {
            return DateUtil.getStringDate(new java.sql.Date(((Timestamp) t).getTime()), "yyyy-MM-dd h:mm:ss a");
        } else {
            return t;
        }
    }

    /**
     * Load master DB foreign key relationships not fetched from tenant retrieval
     * <p>
     * Repositories will need to be Autowired to fetch records
     */

    public double safeValue(BigDecimal o) {
        if (o == null) return 0;
        return o.doubleValue();
    }

    public Long safeValue(Long o) {
        if (o == null) return 0L;
        return o;
    }

    public String safeValue(Object o) {
        if (o == null) return "";
        if (o instanceof java.sql.Date)
            return DateUtil.getStringDate((java.sql.Date) o);
        return o.toString();
    }

    public String safeValue(Boolean o) {
        return Boolean.TRUE.equals(o) ? "Y" : "N";
    }

    public Specification<T> createAddressSpecification(String address, String cityField, String stateField, String streetField, String zipField) {
        Specification<T> addressSpec = null;
        if (Strings.isNullOrEmpty(address)) return addressSpec;
        Pattern containsNumber = Pattern.compile("[A-Z]+[0-9]+[A-Z]+");
        Pattern endsWithNumber = Pattern.compile("[A-Z]+[0-9]+");
        Pattern isNumber = Pattern.compile("[0-9]+");
        Pattern startsWithNumber = Pattern.compile("[0-9]+[A-Z]+");
        List<String> streetNoise = Arrays.asList("AND", "APARTMENT", "APT", "AVE", "AVENUE", "BLDG", "BLVD", "BOULEVARD", "BUILDING", "CENTER", "CTR", "DRIVE", "EAST", "FLOOR", "LOOP", "NORTH", "NORTHEAST", "NORTHWEST", "PARKWAY", "PKWY", "PLACE", "SOUTH", "SOUTHEAST", "SOUTHWEST", "SQUARE", "STE", "STREET", "SUITE", "THE", "TRAIL", "TRL", "UNIT", "WAY", "WEST");
        List<String> usStates = Arrays.asList("AK", "AL", "AR", "AZ", "CA", "CO", "CT", "DC", "DE", "FL", "GA", "HI", "IA", "ID", "IL", "IN", "KS", "KY", "LA", "MA", "MD", "ME", "MI", "MN", "MO", "MS", "MT", "NC", "ND", "NE", "NH", "NJ", "NM", "NV", "NY", "OH", "OK", "OR", "PA", "RI", "SC", "SD", "TN", "TX", "UT", "VA", "VT", "WA", "WI", "WV", "WY");
        String[] addressTokens = address.toUpperCase()
                // Replace "P.O.Box 1234" with "POBOX1234" (special case scenario)
                .replaceAll("P[ \\.]*O[ \\.]*BOX[ ]*", "POBOX")
                // Replace all punctuation with spaces (this will break zip codes like 12345-1234 into two tokens, but I care only about the first 5 digits)
                .replaceAll("[^0-9A-Z]+", " ")
                .split("[ ]+");
        List<Specification<T>> stateSpecs = new ArrayList<>();
        List<Specification<T>> streetSpecs = new ArrayList<>();
        List<Specification<T>> zipSpecs = new ArrayList<>();
        for (int i = 0; i < addressTokens.length; i++) {
            String token = addressTokens[i];
            // Strings like E21ST, E21 or 21ST are likely parts of street address
            Boolean isStreet = ((i + 1) < addressTokens.length && streetNoise.contains(addressTokens[i + 1]))
                    || containsNumber.matcher(token).matches()
                    || endsWithNumber.matcher(token).matches()
                    || startsWithNumber.matcher(token).matches();
            if (token != null && token.length() > 1 && !streetNoise.contains(token)) {
                // There can be multiple states, hence the "OR"
                if (usStates.contains(token)) {
                    stateSpecs.add((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get(stateField), token));
                } else if (token.startsWith("POBOX")) {
                    // Remove the "POBOX" from the string
                    streetSpecs.add((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.like(root.get(streetField), "%" + token.substring(5) + "%"));
                } else if (token.length() > 2) {
                    if (isNumber.matcher(token).matches()) {
                        // There can be multiple zip codes, hence the "OR"
                        if (token.length() == 5) {
                            zipSpecs.add((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.like(root.get(zipField), token + "%"));
                        } else {
                            // Do nothing, I don't care about building and suite numbers
                        }
                    } else {
                        streetSpecs.add((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.like(root.get(streetField), "%" + token + "%"));
                        if (!isStreet) {
                            streetSpecs.add((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.like(root.get(cityField), "%" + token + "%"));
                        }
                    }
                }
            }
        }

        // Internally, all variations of address parts are joined with "OR", e.g. "WHERE zipcode = '12345' OR zipcode = '54321'"
        List<Specification<T>> addressSpecs = new ArrayList<>();
        if ((stateSpecs.size() + streetSpecs.size() + zipSpecs.size()) > 0) {
            if (stateSpecs.size() > 0) addressSpecs.add(combineSpecifications(stateSpecs, null, "OR"));
            if (streetSpecs.size() > 0) addressSpecs.add(combineSpecifications(streetSpecs, null, "OR"));
            if (zipSpecs.size() > 0) addressSpecs.add(combineSpecifications(zipSpecs, null, "OR"));
        }

        // Address parts are connected with AND
        // e.g. "16540 NE AMSTERDAM AVE, NEW YORK, NY 10041-0041" translates into:
        // AND (city LIKE '%AMSTERDAM%' OR city LIKE '%NEW%' OR city LIKE '%YORK%')
        // AND (street_address LIKE '%AMSTERDAM%' OR street_address LIKE '%NEW%' OR street_address LIKE '%YORK%')
        // AND (state = 'NE' OR state = 'NY')
        // AND (zipcode LIKE '16540%' OR zipcode LIKE '10041%')
        // Alternatively, "P.O. Box 16540-0041, PHILADELPHIA, PA 19147" translates to:
        // AND (city LIKE '%PHILADELPHIA%') AND (street_address LIKE '%16540%')
        // AND (state = 'PA') AND (zipcode like '19147%')
        if (addressSpecs.size() > 0) addressSpec = combineSpecifications(addressSpecs, null, "AND");
        return addressSpec;
    }

    public String createAddressWhereClause(String address, String cityColumn, String stateColumn, String streetColumn, String zipColumn) {
        String addressClause = "";
        if (Strings.isNullOrEmpty(address)) return addressClause;
        Pattern containsNumber = Pattern.compile("[A-Z]+[0-9]+[A-Z]+");
        Pattern endsWithNumber = Pattern.compile("[A-Z]+[0-9]+");
        Pattern isNumber = Pattern.compile("[0-9]+");
        Pattern startsWithNumber = Pattern.compile("[0-9]+[A-Z]+");
        List<String> streetNoise = Arrays.asList("AND", "APARTMENT", "APT", "AVE", "AVENUE", "BLDG", "BLVD", "BOULEVARD", "BUILDING", "CENTER", "CTR", "DRIVE", "EAST", "FLOOR", "LOOP", "NORTH", "NORTHEAST", "NORTHWEST", "PARKWAY", "PKWY", "PLACE", "SOUTH", "SOUTHEAST", "SOUTHWEST", "SQUARE", "STE", "STREET", "SUITE", "THE", "TRAIL", "TRL", "UNIT", "WAY", "WEST");
        List<String> usStates = Arrays.asList("AK", "AL", "AR", "AZ", "CA", "CO", "CT", "DC", "DE", "FL", "GA", "HI", "IA", "ID", "IL", "IN", "KS", "KY", "LA", "MA", "MD", "ME", "MI", "MN", "MO", "MS", "MT", "NC", "ND", "NE", "NH", "NJ", "NM", "NV", "NY", "OH", "OK", "OR", "PA", "RI", "SC", "SD", "TN", "TX", "UT", "VA", "VT", "WA", "WI", "WV", "WY");
        String[] addressTokens = address.toUpperCase()
                // Replace "P.O.Box 1234" with "POBOX1234" (special case scenario)
                .replaceAll("P[ \\.]*O[ \\.]*BOX[ ]*", "POBOX")
                // Replace all punctuation with spaces (this will break zip codes like 12345-1234 into two tokens, but I care only about the first 5 digits)
                .replaceAll("[^0-9A-Z]+", " ")
                .split("[ ]+");

        List<String> stateMap = new ArrayList<>();
        List<String> streetMap = new ArrayList<>();
        List<String> zipMap = new ArrayList<>();
        for (int i = 0; i < addressTokens.length; i++) {
            String token = addressTokens[i];
            // Strings like E21ST, E21 or 21ST are likely parts of street address
            Boolean isStreet = ((i + 1) < addressTokens.length && streetNoise.contains(addressTokens[i + 1]))
                    || containsNumber.matcher(token).matches()
                    || endsWithNumber.matcher(token).matches()
                    || startsWithNumber.matcher(token).matches();
            if (token != null && token.length() > 1 && !streetNoise.contains(token)) {
                if (usStates.contains(token)) {
                    stateMap.add("`" + stateColumn + "` = '" + token + "'");
                } else if (token.startsWith("POBOX")) {
                    // Remove the "POBOX" from the string
                    streetMap.add("`" + streetColumn + "` LIKE '%" + token.substring(5) + "%'");
                } else if (token.length() > 2) {
                    if (isNumber.matcher(token).matches()) {
                        if (token.length() == 5) {
                            zipMap.add("`" + zipColumn + "` LIKE '" + token + "%'");
                        } else {
                            // Do nothing, I don't care about building and suite numbers
                        }
                    } else {
                        streetMap.add("`" + streetColumn + "` LIKE '%" + token + "%'");
                        if (!isStreet) {
                            streetMap.add("`" + cityColumn + "` LIKE '%" + token + "%'");
                        }
                    }
                }
            }
        }

        List<String> whereClauses = new ArrayList<>();
        // Inside a column-defined clause, the statements are joined with OR
        // (except for city and street which are bundled together in streetMap)
        if (stateMap.size() > 0) whereClauses.add(String.join(" OR ", stateMap));
        if (streetMap.size() > 0) whereClauses.add(String.join(" OR ", streetMap));
        if (zipMap.size() > 0) whereClauses.add(String.join(" OR ", zipMap));
        // Different column-defined clauses are joined with AND
        if (whereClauses.size() > 0) addressClause = "(" + String.join(") AND (", whereClauses) + ")";

        return addressClause;
    }

    /**
     * Create a single Specification from a list of those
     *
     * @param params - List<Specification> to combine
     * @param spec - the Specification to add to (can be null)
     * @param union - either "AND" or "OR"
     * @return
     */
    public Specification<T> combineSpecifications(List<Specification<T>> params, Specification<T> spec, String union) {
        if (params == null || params.isEmpty()) return spec;

        int startWith = 0;
        if (spec == null) {
            startWith = 1;
            spec = params.get(0);
        }
        for (int i = startWith; i < params.size(); i++) {
            switch (union.toUpperCase()) {
                case "AND":
                    spec = Specification.where(spec).and(params.get(i));
                    break;
                case "OR":
                    spec = Specification.where(spec).or(params.get(i));
                    break;
                default:
                    break;
            }
        }
        return spec;
    }

    public void loadPLBInfo(Payment p, AutoPostPatient autoPostPatient) {
        p.setPaymentType("adjustment");
        p.setPayerType("adjustment");
        p.setAdjustment(autoPostPatient.getAdjustment().abs());
        p.setUnappliedAdjustment(p.getAdjustment().abs());
        String[] transaction = autoPostPatient.getTransactionMessage().split(": ");
        if (transaction.length > 1) {
            String[] trans = transaction[1].split(" ");
            for (String entry : trans) {
                if (entry.matches(Constants.PCN_FORMAT)) {
                    p.setClaimId(Long.parseLong(entry.split("C")[1]));
                    p.setPatientId(Long.parseLong(entry.split("C")[0].substring(1)));
                    break;
                }
            }
        }
    }

    @Autowired
    private CompanyRepository companyRepository;
    
    @Autowired
    private BatchDataSourceProvider batchDataSourceProvider;

    /**
     * This method is called through reflection
     *
     * @param includedFields
     * @return flat export
     */
    public List<Map<String, Object>> exportTable(List<String> includedFields) {
        List<Map<String, Object>> results = new ArrayList<>();
        Type type = ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
        try {
            Class<?> obj = Class.forName(type.getTypeName());
            Table table = obj.getAnnotation(Table.class);
            Set<String> columnSet = getColumns(includedFields, obj);
            String columns = String.join(", ", columnSet);
            String sql;
            List<String> excludedTenants = List.of("rmalb", "aalos");
            boolean limitExportResult = !excludedTenants.contains(TenantContext.getCurrentTenant());
            if(limitExportResult) {
                sql = String.format("SELECT %s FROM %s ORDER BY ID DESC LIMIT 20000", columns, table.name());
            } else {
                sql = String.format("SELECT %s FROM %s", columns, table.name());
            }
            System.out.println(sql);
//            Company company = companyRepository.findByKey(TenantContext.getCurrentTenant());
            DataSource dataSource = batchDataSourceProvider.getTenantDataSource(TenantContext.getCurrentTenant());
            try (Connection conn = dataSource.getConnection();
                 Statement statement = conn.createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
                 ResultSet rs = statement.executeQuery(sql)) {
                String[] colArray = columns.split(", ");
                while (rs.next()) {
                    Map<String, Object> row = new HashMap<>();
                    int i = 1;
                    for (String column : colArray) {
                        Object value = rs.getObject(i);
                        row.put(column, value);
                        i++;
                    }
                    results.add(row);
                }
                rs.close();
            }

        } catch (Exception e) {
            log.error(StringUtil.getExceptionAsString(e));
        }
        return results;
    }

    private static Set<String> getColumns(List<String> includedFields, Class<?> obj) {
        Field[] allFields = obj.getDeclaredFields();
        Set<String> columns = new LinkedHashSet<>();
        for (Field field : allFields) {
            boolean isTableColumn = field.isAnnotationPresent(Id.class) || field.isAnnotationPresent(Version.class) || field.isAnnotationPresent(Column.class);
            if (includedFields.contains(field.getName()) && isTableColumn) {
                String name = "";
                if (field.isAnnotationPresent(Id.class) || field.isAnnotationPresent(Version.class)) {
                    name = field.getName();
                    columns.add(name);
                } else if (field.isAnnotationPresent(Column.class)) {
                    Column column = field.getAnnotation(Column.class);
                    name = "key".equals(column.name()) ? "`key`" : column.name();
                    columns.add(name);
                }
            }
        }
        return columns;
    }

    public abstract void loadForeignKeys(T object);

    /**
     * Implements fundamental saveForVersionFunctionality as follows:
     *  1. Attempts to save the entity object to the DB and collects the error(s)
     *  2. Assesses the potential version lock
     *  4. Returns the response map (NB: the field createdBy is not set!!!)
     * @param newObject
     * @param objectId
     * @return
     */
    public Map<String, Object> saveForVersion(T newObject, Long objectId) {
        Map<String, Object> respMap = new HashMap<>();
        T dbObject;
        try {
            dbObject = save(newObject);
        } catch (Throwable ex) {
            String exceptionMessage = ex.getMessage();
            if (exceptionMessage.contains("org.hibernate.StaleObjectStateException: Row was updated or deleted by another transaction")) {
                dbObject = findOne((ID)objectId);
                String strLockingMessage = "This Item has been edited by another user.  Please reload/re-open to get the latest version and re-save with your changes.";
                respMap = optimisticLockingUtil.checkVersionLock(newObject, dbObject, strLockingMessage, Item.class, objectId);
                respMap = getAuditDetails((ID)objectId, respMap);
                List<LockedEntity> diffs = (List<LockedEntity>) respMap.get(DIFFS);
                // Compare whether the two objects differ only in their version number
                if (diffs.isEmpty() || (diffs.size() == 1 && diffs.get(0).getPropertyName().equals("version"))) {
                    // Two threads tried saving the same object simultaneously
                    // I don't care which one of them won and will return the result
                    respMap.clear();
                } else {
                    // Another user updated this object, OPTIMISTIC_MESSAGE logged
                    dbObject = null;
                }
            } else {
                // Database operation failed for an unrelated reason
                dbObject = null;
                respMap.put(OPTIMISTIC_MESSAGE, exceptionMessage);
            }
        }

        if (dbObject != null) {
            respMap.put(SAVED, dbObject);
        }

        return respMap;
    }
}
