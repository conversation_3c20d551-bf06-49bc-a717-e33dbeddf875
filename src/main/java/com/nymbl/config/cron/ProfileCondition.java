package com.nymbl.config.cron;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.context.annotation.Profile;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.util.MultiValueMap;

/**
 * Created by <PERSON> on 10/18/2019.
 */
public class ProfileCondition implements Condition {

    @Override
    public boolean matches(final ConditionContext context,
                           final AnnotatedTypeMetadata metadata) {
        context.getEnvironment();
        final MultiValueMap<String, Object> attrs = metadata.getAllAnnotationAttributes(Profile.class.getName());
        if (attrs != null) {
            for (final Object value : attrs.get("value")) {
                //Can't override the profile pom settings from application.properties, nymbl.properties, or from the command line.
                //Adding a new property to override the spring.profiles.active property.  This will work from nymbl.properties.
                final String resourcePropertyOverride = context.getEnvironment().getProperty("spring.profiles.active.override");
                final String activeProfiles = context.getEnvironment().getProperty("spring.profiles.active");
                if (resourcePropertyOverride != null) {
                    for (final String profile : (String[]) value) {
                        if (!resourcePropertyOverride.contains(profile)) {
                            return false;
                        }
                    }
                } else {
                    for (final String profile : (String[]) value) {
                        if (activeProfiles == null || !activeProfiles.contains(profile)) {
                            return false;
                        }
                    }
                }
            }
            return true;
        }
        return true;
    }
}
