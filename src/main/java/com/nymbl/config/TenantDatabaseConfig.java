package com.nymbl.config;

import com.nymbl.audit.AuditorAwareImpl;
import jakarta.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import org.hibernate.context.spi.CurrentTenantIdentifierResolver;
import org.hibernate.engine.jdbc.connections.spi.MultiTenantConnectionProvider;
import org.hibernate.envers.configuration.EnversSettings;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.Database;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by Bradley Moore on 10/17/2017.
 */
@Configuration
@ComponentScan("com.nymbl.tenant")
@EnableJpaRepositories(
        entityManagerFactoryRef = "tenantEntityManager",
        transactionManagerRef = "tenantTransactionManager",
        basePackages = {"com.nymbl.tenant.repository", "com.nymbl.tenant.dashboard.repository", "com.nymbl.tenant.interfaces.repository", "com.nymbl.ai.notes.repository"})
@EnableTransactionManagement(proxyTargetClass = true)
@EnableJpaAuditing(auditorAwareRef = "tenantAuditProvider")
public class TenantDatabaseConfig {

    @Bean(name = "tenantEntityManager")
    public LocalContainerEntityManagerFactoryBean tenantEntityManager(DataSource dataSource,
                                                                      MultiTenantConnectionProvider connectionProvider,
                                                                      CurrentTenantIdentifierResolver tenantResolver) {

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        vendorAdapter.setDatabase(Database.valueOf(Database.MYSQL.name()));

        LocalContainerEntityManagerFactoryBean emfBean = new LocalContainerEntityManagerFactoryBean();
        emfBean.setDataSource(dataSource);
        emfBean.setJpaVendorAdapter(vendorAdapter);
        emfBean.setPackagesToScan("com.nymbl.tenant.model", "com.nymbl.payment.model", "com.nymbl.ai.notes.model");
        emfBean.setPersistenceUnitName("tenant");

        Map<String, Object> properties = new HashMap<>();
//        properties.put(org.hibernate.cfg.Environment.PHYSICAL_NAMING_STRATEGY, "org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy");
        properties.put(org.hibernate.cfg.Environment.PHYSICAL_NAMING_STRATEGY, "org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy");
        properties.put(org.hibernate.cfg.Environment.ENABLE_LAZY_LOAD_NO_TRANS, "true");
        properties.put(org.hibernate.cfg.Environment.FORMAT_SQL, "true");
        //Solid gold!
        //properties.put(org.hibernate.cfg.Environment.SHOW_SQL, "true");
        properties.put("hibernate.generate_statistics", false);
//        properties.put(org.hibernate.cfg.Environment.LOG_SLOW_QUERY, (long) 10000);
//        properties.put(org.hibernate.cfg.Environment.MULTI_TENANT, MultiTenancyStrategy.DATABASE);
        properties.put(org.hibernate.cfg.Environment.MULTI_TENANT_CONNECTION_PROVIDER, connectionProvider);
        properties.put(org.hibernate.cfg.Environment.MULTI_TENANT_IDENTIFIER_RESOLVER, tenantResolver);
        properties.put(org.hibernate.cfg.Environment.GLOBALLY_QUOTED_IDENTIFIERS, "true");
        properties.put(org.hibernate.cfg.Environment.KEYWORD_AUTO_QUOTING_ENABLED, "true");
        properties.put(EnversSettings.AUDIT_TABLE_SUFFIX, "_audit");
        properties.put(EnversSettings.REVISION_FIELD_NAME, "revision_id");
        properties.put(EnversSettings.REVISION_TYPE_FIELD_NAME, "revision_type");
        properties.put(EnversSettings.STORE_DATA_AT_DELETE, "true"); // populate the type 2 'delete' record with the final state data

        emfBean.setJpaPropertyMap(properties);
        return emfBean;
    }

    @Bean(name = "tenantTransactionManager")
    public PlatformTransactionManager tenantTransactionManager(@Qualifier("tenantEntityManager") EntityManagerFactory tenantEntityManager) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(tenantEntityManager);
        return transactionManager;
    }

    @Bean(name = "tenantAuditProvider")
    public AuditorAware<String> tenantAuditProvider() {
        return new AuditorAwareImpl();
    }
}
