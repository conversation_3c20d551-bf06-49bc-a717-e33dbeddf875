package com.nymbl.config;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.jmx.JmxReporter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * Configuration for Dropwizard Metrics integration.
 * Provides metrics registry and reporters for connection pool monitoring.
 */
@Configuration
public class MetricsConfig {
    
    @Bean
    public MetricRegistry metricRegistry() {
        MetricRegistry registry = new MetricRegistry();
        
        // JMX Reporter for external monitoring tools
        JmxReporter jmxReporter = JmxReporter.forRegistry(registry)
                .inDomain("nymbl.database")
                .build();
        jmxReporter.start();
        
        // Note: Slf4jReporter requires additional dependency, skip for now
        
        return registry;
    }
} 