package com.nymbl.config;

import com.nymbl.audit.AuditorAwareImpl;
import com.nymbl.database.CompatibleDataSource;
import com.nymbl.database.ConnectionPoolFactory;
import jakarta.persistence.EntityManagerFactory;
import org.hibernate.envers.configuration.EnversSettings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.Database;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by Bradley Moore on 05/16/2017.
 */
@Configuration
//@EnableAspectJAutoProxy(proxyTargetClass = true)
@ComponentScan("com.nymbl.master")
@EnableJpaRepositories(
        entityManagerFactoryRef = "masterEntityManager",
        transactionManagerRef = "masterTransactionManager",
        basePackages = {"com.nymbl.master.repository"}
)
@EnableTransactionManagement(proxyTargetClass = true)
@EnableJpaAuditing(auditorAwareRef = "masterAuditProvider")
public class MasterDatabaseConfig {

    private final Environment environment;
    private final ConnectionPoolFactory connectionPoolFactory;

    @Value("${db.url}")
    String url;

    @Value("${db.username}")
    String username;

    @Value("${db.password}")
    String password;

    @Autowired
    public MasterDatabaseConfig(Environment environment, ConnectionPoolFactory connectionPoolFactory) {
        this.environment = environment;
        this.connectionPoolFactory = connectionPoolFactory;
    }

    @Primary
    @Bean(destroyMethod = "close")
    public DataSource dataSource() {
        // Create database configuration using the factory's inner class
        ConnectionPoolFactory.DatabaseConfig config = new ConnectionPoolFactory.DatabaseConfig("master");
        config.setJdbcUrl(url);
        config.setUsername(username);
        config.setPassword(password);
        config.setDriverClassName("software.aws.rds.jdbc.mysql.Driver");
        
        // Set connection pool size based on environment
        if (Arrays.asList(environment.getActiveProfiles()).contains("local")) {
            config.setMaxPoolSize(20);
            config.setMinPoolSize(5);
        } else {
            config.setMaxPoolSize(1000);
            config.setMinPoolSize(50);
        }
        
        // Set timeouts and maintenance settings
        config.setConnectionTimeout(30000); // 30 seconds
        config.setIdleTimeout(600000); // 10 minutes
        config.setMaxLifetime(1800000); // 30 minutes
        config.setLeakDetectionThreshold(60000); // 1 minute
        
        // Validation query
        config.setValidationQuery("SELECT 1");
        
        // Use factory to create the DataSource
        DataSource rawDataSource = connectionPoolFactory.createDataSource(config);
        
        // Wrap in CompatibleDataSource for monitoring and lifecycle management
        return new CompatibleDataSource(rawDataSource, "master", 
            connectionPoolFactory.isHikariEnabled() ? "HikariCP" : "TomcatJDBC");
    }

    @Primary
    @Bean(name = "masterEntityManager")
    public LocalContainerEntityManagerFactoryBean masterEntityManager() {
        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        vendorAdapter.setDatabase(Database.valueOf("MYSQL"));

        LocalContainerEntityManagerFactoryBean factoryBean = new LocalContainerEntityManagerFactoryBean();
        factoryBean.setDataSource(dataSource());
        factoryBean.setJpaVendorAdapter(vendorAdapter);
        factoryBean.setPackagesToScan("com.nymbl.master.model");
        factoryBean.setPersistenceUnitName("master");

        Map<String, Object> properties = new HashMap<>();
//        properties.put(org.hibernate.cfg.Environment.PHYSICAL_NAMING_STRATEGY, "org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy");
        properties.put(org.hibernate.cfg.Environment.PHYSICAL_NAMING_STRATEGY, "org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy");
        properties.put(org.hibernate.cfg.Environment.FORMAT_SQL, "true");
        //Solid gold!
        //properties.put(org.hibernate.cfg.Environment.SHOW_SQL, "true");
        properties.put(EnversSettings.AUDIT_TABLE_SUFFIX, "_audit");
        properties.put(EnversSettings.REVISION_FIELD_NAME, "revision_id");
        properties.put(EnversSettings.REVISION_TYPE_FIELD_NAME, "revision_type");
        properties.put(EnversSettings.STORE_DATA_AT_DELETE, "true");// populate the type 2 'delete' record with the final state data

        properties.put(org.hibernate.cfg.Environment.ENABLE_LAZY_LOAD_NO_TRANS, "true");
        factoryBean.setJpaPropertyMap(properties);

        return factoryBean;
    }

    @Primary
    @Bean(name = "masterTransactionManager")
    public PlatformTransactionManager masterTransactionManager(@Qualifier("masterEntityManager") EntityManagerFactory masterEntityManager) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(masterEntityManager);
        return transactionManager;
    }

    @Bean(name = "masterAuditProvider")
    public AuditorAware<String> masterAuditProvider() {
        return new AuditorAwareImpl();
    }

}
