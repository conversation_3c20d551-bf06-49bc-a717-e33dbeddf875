package com.nymbl.tenant;

import com.nymbl.config.TenantDatabaseConfig;
import com.nymbl.master.model.Company;
import com.nymbl.master.repository.CompanyRepository;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.engine.jdbc.connections.spi.AbstractDataSourceBasedMultiTenantConnectionProviderImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.io.Serial;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON> on 10/17/2017.
 */
@Slf4j
@Component
public class MultiTenantConnectionProviderImpl extends AbstractDataSourceBasedMultiTenantConnectionProviderImpl implements ApplicationListener<ContextRefreshedEvent> {

    @Serial
    private static final long serialVersionUID = 1L;

    @Value("${db.url:*****************************************************}")
    private String url;

    // Parameter Store property to enable HikariCP
    @Value("${nymbl.database.hikari.enabled:false}")
    boolean hikariEnabled;

    public static Map<String, DataSource> map; // map holds the companyKey => DataSource

    private final CompanyRepository companyRepository;
    private final DataSource dataSource;
    private final TenantDatabaseConfig tenantDatabaseConfig;
    private final CurrentTenantResolverImpl currentTenantResolver;
    private final Environment environment;

    @Autowired
    public MultiTenantConnectionProviderImpl(@Lazy CompanyRepository companyRepository,
                                             DataSource dataSource,
                                             TenantDatabaseConfig tenantDatabaseConfig,
                                             CurrentTenantResolverImpl currentTenantResolver,
                                             Environment environment) {
        this.companyRepository = companyRepository;
        this.dataSource = dataSource;
        this.tenantDatabaseConfig = tenantDatabaseConfig;
        this.currentTenantResolver = currentTenantResolver;
        this.environment = environment;
    }

    @PostConstruct
    public void load() {
        map = new HashMap<>();
    }

    public void init() {
        load();
        List<Company> companies = companyRepository.findAllByActiveTrue();
        for (Company company : companies) {
            try {
                addTenant(company);
            } catch (Exception e) {
                log.error("Error in database URL " + url.replace("nymbl_master", company.getKey()), e);
            }
        }
    }

    @Override
    protected DataSource selectAnyDataSource() {
        return map.getOrDefault(TenantContext.getCurrentTenant(), dataSource);
    }

    @Override
    protected DataSource selectDataSource(Object tenantIdentifier) {
        if (!map.containsKey((String) tenantIdentifier)) {
            // Possible new company added to DB since last restart.
            Company c = companyRepository.findByKey(tenantIdentifier.toString());
            if (c != null && c.getActive()) {
                addTenant(c);
            } else {
                log.error("Datasource is not found for " + tenantIdentifier + ".  Returning default value of nymbl_master.  This tenant is probably not set to active in the nymbl_master.user table");
            }
        }
        return map.getOrDefault(tenantIdentifier, dataSource);
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        // This is super critical to initialize after application is done with configuring beans.
        // otherwise you can not use companyRepository to fetch all the companies
        init();
    }

    public void addTenant(Company company) {
        DataSource ds;

        if (hikariEnabled) {
            // Create HikariCP DataSource
            ds = createHikariTenantDataSource(company);
        } else {
            // Create original Tomcat JDBC DataSource
            ds = createTomcatTenantDataSource(company);
        }

        tenantDatabaseConfig.tenantEntityManager(ds, this, currentTenantResolver);
        map.put(company.getKey(), ds);
    }

    private DataSource createHikariTenantDataSource(Company company) {
        HikariConfig config = new HikariConfig();
        String companyUrl = url.replace("nymbl_master", company.getKey());
        boolean hasParams = companyUrl.contains("?");
        config.setJdbcUrl(companyUrl + (hasParams ? "&" : "?") + "createDatabaseIfNotExist=true");
        config.setUsername(company.getKey());
        config.setPassword(company.getKey());
        config.setDriverClassName("software.aws.rds.jdbc.mysql.Driver");

        if (Arrays.asList(environment.getActiveProfiles()).contains("cron")) {
            config.setMaximumPoolSize(50);
            config.setMinimumIdle(5);
            config.setConnectionTimeout(30000);
            config.setIdleTimeout(600000);
            config.setMaxLifetime(1200000);
            config.setLeakDetectionThreshold(60000);
        } else {
            Integer dbConnections = company.getDatabaseConnections();
            if (dbConnections == null || dbConnections <= 0) {
                dbConnections = 20; // Default fallback
            }
            config.setMaximumPoolSize(dbConnections);
            config.setMinimumIdle(Math.max(3, dbConnections / 4));
            config.setConnectionTimeout(30000);
            config.setIdleTimeout(600000);
            config.setMaxLifetime(600000);
            config.setLeakDetectionThreshold(60000);
        }

        config.setConnectionTestQuery("SELECT 1");
        config.setPoolName(company.getKey() + "-hikari-pool");

        return new HikariDataSource(config);
    }

    private DataSource createTomcatTenantDataSource(Company company) {
        // Original Tomcat JDBC implementation
        org.apache.tomcat.jdbc.pool.DataSource ds = new org.apache.tomcat.jdbc.pool.DataSource();
        String companyUrl = url.replace("nymbl_master", company.getKey());
        boolean hasParams = companyUrl.contains("?");
        ds.setUrl(companyUrl + (hasParams ? "&" : "?") + "createDatabaseIfNotExist=true");
        ds.setUsername(company.getKey());
        ds.setPassword(company.getKey());
        ds.setDriverClassName("software.aws.rds.jdbc.mysql.Driver");

        if (Arrays.asList(environment.getActiveProfiles()).contains("cron")) {
            ds.setMaxActive(50);
            ds.setInitialSize(10);
            ds.setMaxIdle(10);
            ds.setMinIdle(5);
            ds.setRemoveAbandonedTimeout(1200);
        } else {
            ds.setMaxActive(company.getDatabaseConnections());
            ds.setInitialSize(company.getDatabaseConnections() / 2);
            ds.setMaxIdle(company.getDatabaseConnections() / 2);
            ds.setMinIdle(3);
            ds.setRemoveAbandonedTimeout(600);
        }

        ds.setTimeBetweenEvictionRunsMillis(30000);
        ds.setMinEvictableIdleTimeMillis(60000);
        ds.setRemoveAbandoned(true);
        ds.setLogValidationErrors(true);
        ds.setTestOnConnect(true);
        ds.setTestWhileIdle(true);
        ds.setTestOnBorrow(true);
        ds.setValidationQuery("SELECT 1");

        return ds;
    }


}
