package com.nymbl.tenant;

import com.nymbl.config.TenantDatabaseConfig;
import com.nymbl.database.CompatibleDataSource;
import com.nymbl.database.ConnectionPoolFactory;
import com.nymbl.master.model.Company;
import com.nymbl.master.repository.CompanyRepository;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.engine.jdbc.connections.spi.AbstractDataSourceBasedMultiTenantConnectionProviderImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.io.Serial;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by Bradley Moore on 10/17/2017.
 */
@Component
public class MultiTenantConnectionProviderImpl extends AbstractDataSourceBasedMultiTenantConnectionProviderImpl implements ApplicationListener<ContextRefreshedEvent> {

    @Serial
    private static final long serialVersionUID = 1L;

    private static final Logger log = LoggerFactory.getLogger(MultiTenantConnectionProviderImpl.class);

    @Value("${db.url:*****************************************************}")
    private String url;

    public static ConcurrentHashMap<String, DataSource> map; // thread-safe map holds the companyKey => DataSource

    private final CompanyRepository companyRepository;
    private final DataSource dataSource;
    private final TenantDatabaseConfig tenantDatabaseConfig;
    private final CurrentTenantResolverImpl currentTenantResolver;
    private final Environment environment;
    private final ConnectionPoolFactory connectionPoolFactory;

    @Autowired
    public MultiTenantConnectionProviderImpl(@Lazy CompanyRepository companyRepository,
                                             DataSource dataSource,
                                             TenantDatabaseConfig tenantDatabaseConfig,
                                             CurrentTenantResolverImpl currentTenantResolver,
                                             Environment environment,
                                             ConnectionPoolFactory connectionPoolFactory) {
        this.companyRepository = companyRepository;
        this.dataSource = dataSource;
        this.tenantDatabaseConfig = tenantDatabaseConfig;
        this.currentTenantResolver = currentTenantResolver;
        this.environment = environment;
        this.connectionPoolFactory = connectionPoolFactory;
    }

    @PostConstruct
    public void load() {
        map = new ConcurrentHashMap<>();
    }

    public void init() {
        load();
        List<Company> companies = companyRepository.findAllByActiveTrue();
        for (Company company : companies) {
            try {
                addTenant(company);
            } catch (Exception e) {
                log.error("Error in database URL " + url.replace("nymbl_master", company.getKey()), e);
            }
        }
    }

    @Override
    protected DataSource selectAnyDataSource() {
        return map.getOrDefault(TenantContext.getCurrentTenant(), dataSource);
    }

    @Override
    protected DataSource selectDataSource(Object tenantIdentifier) {
        if (!map.containsKey((String) tenantIdentifier)) {
            // Possible new company added to DB since last restart.
            Company c = companyRepository.findByKey(tenantIdentifier.toString());
            if (c != null && c.getActive()) {
                addTenant(c);
            } else {
                log.error("Datasource is not found for " + tenantIdentifier + ".  Returning default value of nymbl_master.  This tenant is probably not set to active in the nymbl_master.user table");
            }
        }
        return map.getOrDefault(tenantIdentifier, dataSource);
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        // This is super critical to initialize after application is done with configuring beans.
        // otherwise you can not use companyRepository to fetch all the companies
        init();
    }

    public void addTenant(Company company) {
        // Use computeIfAbsent for thread-safe check-then-act pattern
        map.computeIfAbsent(company.getKey(), key -> {
            log.info("Creating new DataSource for tenant: {}", key);

            try {
                // Create DatabaseConfig for the ConnectionPoolFactory
                ConnectionPoolFactory.DatabaseConfig config = new ConnectionPoolFactory.DatabaseConfig(key);
                String companyUrl = url.replace("nymbl_master", key);
                boolean hasParams = companyUrl.contains("?");
                config.setJdbcUrl(companyUrl + (hasParams ? "&" : "?") + "createDatabaseIfNotExist=true");
                config.setUsername(key);
                config.setPassword(key);
                config.setDriverClassName("software.aws.rds.jdbc.mysql.Driver");

                // Set pool sizing based on environment
                if (Arrays.asList(environment.getActiveProfiles()).contains("cron")) {
                    config.setMaxPoolSize(50);
                    config.setMinPoolSize(5);
                    config.setConnectionTimeout(30000);
                    config.setIdleTimeout(600000);
                    config.setMaxLifetime(1200000);
                    config.setLeakDetectionThreshold(60000);
                } else {
                    Integer dbConnections = company.getDatabaseConnections();
                    if (dbConnections == null || dbConnections <= 0) {
                        dbConnections = 20; // Default fallback
                    }
                    config.setMaxPoolSize(dbConnections);
                    config.setMinPoolSize(Math.max(3, dbConnections / 4));
                    config.setConnectionTimeout(30000);
                    config.setIdleTimeout(600000);
                    config.setMaxLifetime(600000);
                    config.setLeakDetectionThreshold(60000);
                }

                // Use ConnectionPoolFactory to create the appropriate DataSource
                DataSource rawDataSource = connectionPoolFactory.createDataSource(config);

                // Wrap in CompatibleDataSource for unified interface
                CompatibleDataSource compatibleDataSource = new CompatibleDataSource(
                    rawDataSource, key, connectionPoolFactory.isHikariEnabled() ? "hikari" : "tomcat");

                // Configure entity manager
                tenantDatabaseConfig.tenantEntityManager(compatibleDataSource, this, currentTenantResolver);

                log.info("Successfully created DataSource for tenant: {} using pool type: {}",
                    key, connectionPoolFactory.isHikariEnabled() ? "HikariCP" : "Tomcat JDBC");

                return compatibleDataSource;

            } catch (Exception e) {
                log.error("Failed to create DataSource for tenant: {}", key, e);
                throw new RuntimeException("Failed to create DataSource for tenant: " + key, e);
            }
        });
    }

    /**
     * Get the tenant DataSources map for management operations.
     */
    public ConcurrentHashMap<String, DataSource> getTenantDataSources() {
        return map;
    }

    /**
     * Add a tenant DataSource.
     */
    public void addTenant(String tenantKey, DataSource dataSource) {
        if (tenantKey != null && dataSource != null) {
            map.put(tenantKey, dataSource);
            log.info("Added tenant DataSource: {}", tenantKey);
        }
    }

    /**
     * Remove a tenant DataSource with proper resource cleanup.
     */
    public void removeTenant(String tenantKey) {
        if (tenantKey == null || tenantKey.trim().isEmpty()) {
            log.warn("Cannot remove tenant with null or empty key");
            return;
        }

        DataSource dataSource = map.get(tenantKey);
        if (dataSource == null) {
            log.debug("No DataSource found for tenant: {}", tenantKey);
            return;
        }

        try {
            // Close the DataSource first
            closeDataSource(dataSource);

            // Only remove from map after successful closure
            DataSource removed = map.remove(tenantKey);
            if (removed != null) {
                log.info("Successfully removed tenant DataSource: {}", tenantKey);
            }
        } catch (Exception e) {
            log.error("Failed to close DataSource for tenant: {}, keeping in map", tenantKey, e);
            throw new RuntimeException("Failed to remove tenant DataSource: " + tenantKey, e);
        }
    }

    /**
     * Close a DataSource handling different types appropriately.
     */
    private void closeDataSource(DataSource dataSource) throws Exception {
        if (dataSource instanceof CompatibleDataSource) {
            ((CompatibleDataSource) dataSource).close();
        } else if (dataSource instanceof com.zaxxer.hikari.HikariDataSource) {
            ((com.zaxxer.hikari.HikariDataSource) dataSource).close();
        } else if (dataSource instanceof org.apache.tomcat.jdbc.pool.DataSource) {
            ((org.apache.tomcat.jdbc.pool.DataSource) dataSource).close();
        } else {
            log.warn("Unknown DataSource type: {}, cannot close gracefully", dataSource.getClass().getName());
        }
    }
}
