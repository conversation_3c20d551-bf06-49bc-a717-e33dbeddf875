package com.nymbl.tenant.batch.config;

import com.nymbl.config.QuicksightCsvSQL;
import com.nymbl.database.BatchDataSourceProvider;
import com.nymbl.tenant.batch.dto.QuicksightUserTask;
import com.nymbl.tenant.batch.listeners.AwsJobListener;
import com.nymbl.tenant.batch.listeners.AwsStepListener;
import com.nymbl.tenant.batch.listeners.ChunkCountListener;
import com.nymbl.tenant.batch.processors.UserTaskRowMapper;
import com.nymbl.tenant.batch.service.CSVTransferS3Quicksight;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.configuration.support.DefaultBatchConfiguration;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.Order;
import org.springframework.batch.item.database.support.MySqlPagingQueryProvider;
import org.springframework.batch.item.file.FlatFileItemWriter;
import org.springframework.batch.item.file.builder.FlatFileItemWriterBuilder;
import org.springframework.batch.item.file.transform.BeanWrapperFieldExtractor;
import org.springframework.batch.item.file.transform.DelimitedLineAggregator;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.WritableResource;
import org.springframework.transaction.PlatformTransactionManager;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Bradley Moore on 8/20/24.
 */
@Slf4j
@Configuration
public class UserTaskBatchConfiguration extends DefaultBatchConfiguration {

    @Qualifier("tenantTransactionManager")
    public final PlatformTransactionManager tenantTransactionManager;
    private final AwsStepListener awsStepListener;
    private final CSVTransferS3Quicksight csvTransferS3Quicksight;
    private final BatchDataSourceProvider batchDataSourceProvider;

    public UserTaskBatchConfiguration(PlatformTransactionManager tenantTransactionManager,
                                      AwsStepListener awsStepListener,
                                      CSVTransferS3Quicksight csvTransferS3Quicksight,
                                      BatchDataSourceProvider batchDataSourceProvider) {
        this.tenantTransactionManager = tenantTransactionManager;
        this.awsStepListener = awsStepListener;
        this.csvTransferS3Quicksight = csvTransferS3Quicksight;
        this.batchDataSourceProvider = batchDataSourceProvider;
    }

    @NotNull
    @Override
    protected PlatformTransactionManager getTransactionManager() {
        return tenantTransactionManager;
    }

    @Bean(name = "userTaskCSVToAwsJob")
    public Job userTaskCSVToAwsJob(AwsJobListener listener, JobRepository jobRepository) {
        return new JobBuilder("userTaskCSVToAwsJob", jobRepository)
                .incrementer(new RunIdIncrementer())
                .listener(listener)
                .flow(toQsUserTaskCreateTempFile(jobRepository))
                .next(toQsUserTaskAwsStep(jobRepository))
                .end()
                .build();
    }

    @Bean
    public Step toQsUserTaskCreateTempFile(JobRepository jobRepository) {
        return new StepBuilder("toQsUserTaskCreateTempFile", jobRepository)
                .<QuicksightUserTask, QuicksightUserTask>chunk(5000, tenantTransactionManager)
                .reader(qsUserTaskJdbcPagingItemReader(null))
                .writer(qsUserTaskFlatFileItemWriter(null, null))
                .listener(new ChunkCountListener("User Task"))
                .listener(awsStepListener)
                .build();
    }

    @Bean
    public Step toQsUserTaskAwsStep(JobRepository jobRepository) {
        return new StepBuilder("toQsUserTaskAwsStep", jobRepository).tasklet(csvTransferS3Quicksight, tenantTransactionManager).build();
    }

    @Bean
    @StepScope
    public JdbcPagingItemReader<QuicksightUserTask> qsUserTaskJdbcPagingItemReader(@Value("#{jobParameters['tenant']}") String tenant) {
        JdbcPagingItemReader<QuicksightUserTask> reader = new JdbcPagingItemReader<>();
        reader.setDataSource(batchDataSourceProvider.getTenantDataSource(tenant));
        reader.setPageSize(50000);
        reader.setFetchSize(50000);

        MySqlPagingQueryProvider queryProvider = new MySqlPagingQueryProvider();
        queryProvider.setSelectClause(QuicksightCsvSQL.taskSelect);
        queryProvider.setFromClause("(" + QuicksightCsvSQL.task + ") AS t1");

        Map<String, Order> order = new HashMap<>();
        order.put("prescription_id", Order.ASCENDING);
        queryProvider.setSortKeys(order);

        Map<String, Object> params = new HashMap<>();
        params.put("tenant", tenant);
        reader.setParameterValues(params);
        reader.setQueryProvider(queryProvider);
        reader.setRowMapper(new UserTaskRowMapper());
        try {
            reader.afterPropertiesSet();
        } catch (Exception e) {
            log.error("Error while reading task", e);
        }
        return reader;
    }

    @Bean
    @StepScope
    public FlatFileItemWriter<QuicksightUserTask> qsUserTaskFlatFileItemWriter(@Value("#{jobParameters['tenant']}") String tenant, @Value("#{jobParameters['filePath']}") String filePath) {
        Field[] fields = QuicksightUserTask.class.getDeclaredFields();
        List<String> names = Arrays.stream(fields).map(Field::getName).toList();
        String[] arrayFieldNames = new String[names.size()];
        arrayFieldNames = names.toArray(arrayFieldNames);
        final String header = String.join(",", arrayFieldNames);

        BeanWrapperFieldExtractor<QuicksightUserTask> fieldExtractor = new BeanWrapperFieldExtractor<>();
        fieldExtractor.setNames(arrayFieldNames);
        fieldExtractor.afterPropertiesSet();
        DelimitedLineAggregator<QuicksightUserTask> lineAggregator = new DelimitedLineAggregator<>();
        lineAggregator.setFieldExtractor(fieldExtractor);
        WritableResource resource = new FileSystemResource(filePath);
        return new FlatFileItemWriterBuilder<QuicksightUserTask>()
                .name("itemWriter")
                .append(true)
                .shouldDeleteIfEmpty(false)
                .shouldDeleteIfExists(true)
                .lineAggregator(lineAggregator)
                .resource(resource)
                .headerCallback(writer -> writer.write(header))
                .build();
    }
}
