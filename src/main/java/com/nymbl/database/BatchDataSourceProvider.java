package com.nymbl.database;

import com.nymbl.tenant.MultiTenantConnectionProviderImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

/**
 * Unified provider for DataSource access in batch operations.
 * Provides thread-safe access to both master and tenant DataSources.
 */
@Component
public class BatchDataSourceProvider {
    
    private static final Logger logger = LoggerFactory.getLogger(BatchDataSourceProvider.class);
    
    @Autowired
    private MultiTenantConnectionProviderImpl connectionProvider;
    
    @Autowired
    private DataSource masterDataSource;
    
    /**
     * Get the master DataSource.
     */
    public DataSource getMasterDataSource() {
        logger.debug("Providing master DataSource");
        return masterDataSource;
    }
    
    /**
     * Get a tenant-specific DataSource.
     */
    public DataSource getTenantDataSource(String tenantId) {
        if (tenantId == null || tenantId.trim().isEmpty()) {
            throw new IllegalArgumentException("Tenant ID cannot be null or empty");
        }
        
        logger.debug("Providing DataSource for tenant: {}", tenantId);
        
        try {
            // Access the public static map directly since selectDataSource is protected
            DataSource dataSource = MultiTenantConnectionProviderImpl.map.get(tenantId);
            if (dataSource == null) {
                throw new IllegalStateException("No DataSource found for tenant: " + tenantId);
            }
            
            return dataSource;
        } catch (Exception e) {
            logger.error("Failed to get DataSource for tenant: {}", tenantId, e);
            throw new RuntimeException("Failed to get DataSource for tenant: " + tenantId, e);
        }
    }
    
    /**
     * Get a tenant-specific DataSource with fallback to master if tenant not found.
     */
    public DataSource getTenantDataSourceWithFallback(String tenantId) {
        try {
            return getTenantDataSource(tenantId);
        } catch (Exception e) {
            logger.warn("Failed to get tenant DataSource for: {}, falling back to master", tenantId, e);
            return getMasterDataSource();
        }
    }
    
    /**
     * Check if a tenant DataSource exists.
     */
    public boolean hasTenantDataSource(String tenantId) {
        if (tenantId == null || tenantId.trim().isEmpty()) {
            return false;
        }
        
        try {
            // Access the public static map directly
            DataSource dataSource = MultiTenantConnectionProviderImpl.map.get(tenantId);
            return dataSource != null;
        } catch (Exception e) {
            logger.debug("Tenant DataSource check failed for: {}", tenantId, e);
            return false;
        }
    }
    
    /**
     * Get pool statistics for a tenant DataSource if it's a CompatibleDataSource.
     */
    public CompatibleDataSource.PoolStats getTenantPoolStats(String tenantId) {
        try {
            DataSource dataSource = getTenantDataSource(tenantId);
            if (dataSource instanceof CompatibleDataSource) {
                return ((CompatibleDataSource) dataSource).getPoolStats();
            }
        } catch (Exception e) {
            logger.debug("Could not retrieve pool stats for tenant: {}", tenantId, e);
        }
        
        return new CompatibleDataSource.PoolStats(0, 0, 0, 0, "unknown");
    }
    
    /**
     * Get pool statistics for the master DataSource if it's a CompatibleDataSource.
     */
    public CompatibleDataSource.PoolStats getMasterPoolStats() {
        try {
            if (masterDataSource instanceof CompatibleDataSource) {
                return ((CompatibleDataSource) masterDataSource).getPoolStats();
            }
        } catch (Exception e) {
            logger.debug("Could not retrieve master pool stats", e);
        }
        
        return new CompatibleDataSource.PoolStats(0, 0, 0, 0, "unknown");
    }
} 