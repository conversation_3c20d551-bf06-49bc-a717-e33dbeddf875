package com.nymbl.database;

import com.codahale.metrics.Gauge;
import com.codahale.metrics.MetricRegistry;
import com.nymbl.tenant.MultiTenantConnectionProviderImpl;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service for monitoring database connection pools and providing metrics.
 */
//@Service // Temporarily disabled for testing
public class DatabaseMonitoringService {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseMonitoringService.class);

    private final MetricRegistry metricRegistry;
    private final MultiTenantConnectionProviderImpl connectionProvider;

    @Autowired
    public DatabaseMonitoringService(MetricRegistry metricRegistry,
                                     MultiTenantConnectionProviderImpl connectionProvider) {
        this.metricRegistry = metricRegistry;
        this.connectionProvider = connectionProvider;
        initializeMetrics();
    }

    /**
     * Initialize JMX metrics for all tenant pools.
     */
    private void initializeMetrics() {
        logger.info("Initializing database monitoring metrics...");

        // Register gauge for total tenant count
        metricRegistry.register("database.tenants.total",
            (Gauge<Integer>) () -> {
                ConcurrentHashMap<String, DataSource> tenants = connectionProvider.getTenantDataSources();
                return tenants != null ? tenants.size() : 0;
            });

        // Register gauge for healthy tenant count
        metricRegistry.register("database.tenants.healthy",
            (Gauge<Integer>) this::countHealthyTenants);

        logger.info("Database monitoring metrics initialized");
    }

    /**
     * Clean up metrics on shutdown to prevent memory leaks.
     */
    @PreDestroy
    public void cleanup() {
        logger.info("Cleaning up database monitoring metrics...");
        try {
            // Remove all database-related metrics
            metricRegistry.removeMatching((name, metric) -> name.startsWith("database."));
            logger.info("Database monitoring metrics cleaned up successfully");
        } catch (Exception e) {
            logger.warn("Error cleaning up database monitoring metrics", e);
        }
    }

    /**
     * Count the number of healthy tenants.
     */
    private int countHealthyTenants() {
        try {
            ConcurrentHashMap<String, DataSource> tenants = connectionProvider.getTenantDataSources();
            if (tenants == null) return 0;

            int healthyCount = 0;
            for (String tenantKey : tenants.keySet()) {
                if (isHealthy(tenantKey)) {
                    healthyCount++;
                }
            }
            return healthyCount;
        } catch (Exception e) {
            logger.warn("Error counting healthy tenants", e);
            return 0;
        }
    }

    /**
     * Check if a specific tenant's database connection is healthy.
     */
    public boolean isHealthy(String tenantKey) {
        try {
            ConcurrentHashMap<String, DataSource> tenants = connectionProvider.getTenantDataSources();
            DataSource dataSource = tenants.get(tenantKey);

            if (dataSource == null) {
                return false;
            }

            // Test connection with 5-second timeout
            try (var connection = dataSource.getConnection()) {
                return connection.isValid(5);
            }
        } catch (Exception e) {
            logger.debug("Health check failed for tenant: {}", tenantKey, e);
            return false;
        }
    }

    /**
     * Get detailed metrics for a specific tenant.
     */
    public TenantMetrics getTenantMetrics(String tenantKey) {
        try {
            ConcurrentHashMap<String, DataSource> tenants = connectionProvider.getTenantDataSources();
            DataSource dataSource = tenants.get(tenantKey);

            if (dataSource == null) {
                return new TenantMetrics(tenantKey, false, "DataSource not found", null);
            }

            boolean healthy = isHealthy(tenantKey);
            CompatibleDataSource.PoolStats poolStats = null;

            if (dataSource instanceof CompatibleDataSource) {
                poolStats = ((CompatibleDataSource) dataSource).getPoolStats();
            }

            return new TenantMetrics(tenantKey, healthy,
                                     healthy ? "OK" : "Connection failed",
                                     poolStats);
        } catch (Exception e) {
            logger.warn("Error getting metrics for tenant: {}", tenantKey, e);
            return new TenantMetrics(tenantKey, false, "Error: " + e.getMessage(), null);
        }
    }

    /**
     * Get metrics for all tenants.
     */
    public ConcurrentHashMap<String, TenantMetrics> getAllTenantMetrics() {
        ConcurrentHashMap<String, TenantMetrics> metrics = new ConcurrentHashMap<>();

        try {
            ConcurrentHashMap<String, DataSource> tenants = connectionProvider.getTenantDataSources();
            if (tenants != null) {
                tenants.keySet().forEach(tenantKey ->
                    metrics.put(tenantKey, getTenantMetrics(tenantKey)));
            }
        } catch (Exception e) {
            logger.error("Error getting all tenant metrics", e);
        }

        return metrics;
    }

    /**
     * Periodic health check that logs warnings for unhealthy tenants.
     */
    @Scheduled(fixedRate = 300000) // Every 5 minutes
    public void performHealthChecks() {
        try {
            ConcurrentHashMap<String, DataSource> tenants = connectionProvider.getTenantDataSources();
            if (tenants == null || tenants.isEmpty()) {
                logger.debug("No tenants to health check");
                return;
            }

            int totalTenants = tenants.size();
            int healthyTenants = 0;
            int unhealthyTenants = 0;

            for (String tenantKey : tenants.keySet()) {
                if (isHealthy(tenantKey)) {
                    healthyTenants++;
                } else {
                    unhealthyTenants++;
                    logger.warn("Tenant database connection is unhealthy: {}", tenantKey);
                }
            }

            logger.info("Database health check completed - Total: {}, Healthy: {}, Unhealthy: {}",
                       totalTenants, healthyTenants, unhealthyTenants);

            if (unhealthyTenants > 0) {
                logger.warn("Warning: {} tenant(s) have unhealthy database connections", unhealthyTenants);
            }

        } catch (Exception e) {
            logger.error("Error during periodic health check", e);
        }
    }

    /**
     * Generate a summary report of all database connections.
     */
    public DatabaseSummary getDatabaseSummary() {
        try {
            ConcurrentHashMap<String, DataSource> tenants = connectionProvider.getTenantDataSources();

            int totalTenants = tenants != null ? tenants.size() : 0;
            int healthyTenants = countHealthyTenants();
            int unhealthyTenants = totalTenants - healthyTenants;

            return new DatabaseSummary(totalTenants, healthyTenants, unhealthyTenants);

        } catch (Exception e) {
            logger.error("Error generating database summary", e);
            return new DatabaseSummary(0, 0, 0);
        }
    }

    /**
     * Metrics for a single tenant.
     */
    public static class TenantMetrics {
        private final String tenantKey;
        private final boolean healthy;
        private final String status;
        private final CompatibleDataSource.PoolStats poolStats;

        public TenantMetrics(String tenantKey, boolean healthy, String status,
                           CompatibleDataSource.PoolStats poolStats) {
            this.tenantKey = tenantKey;
            this.healthy = healthy;
            this.status = status;
            this.poolStats = poolStats;
        }

        public String getTenantKey() { return tenantKey; }
        public boolean isHealthy() { return healthy; }
        public String getStatus() { return status; }
        public CompatibleDataSource.PoolStats getPoolStats() { return poolStats; }

        @Override
        public String toString() {
            return String.format("TenantMetrics{tenant='%s', healthy=%s, status='%s', poolStats=%s}",
                               tenantKey, healthy, status, poolStats);
        }
    }

    /**
     * Summary of all database connections.
     */
    public static class DatabaseSummary {
        private final int totalTenants;
        private final int healthyTenants;
        private final int unhealthyTenants;

        public DatabaseSummary(int totalTenants, int healthyTenants, int unhealthyTenants) {
            this.totalTenants = totalTenants;
            this.healthyTenants = healthyTenants;
            this.unhealthyTenants = unhealthyTenants;
        }

        public int getTotalTenants() { return totalTenants; }
        public int getHealthyTenants() { return healthyTenants; }
        public int getUnhealthyTenants() { return unhealthyTenants; }
        public double getHealthPercentage() {
            return totalTenants > 0 ? (double) healthyTenants / totalTenants * 100 : 100.0;
        }

        @Override
        public String toString() {
            return String.format("DatabaseSummary{total=%d, healthy=%d, unhealthy=%d, health=%.1f%%}",
                               totalTenants, healthyTenants, unhealthyTenants, getHealthPercentage());
        }
    }
}