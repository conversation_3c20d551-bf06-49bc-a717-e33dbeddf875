package com.nymbl.database;

import com.nymbl.tenant.MultiTenantConnectionProviderImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service for managing tenant lifecycle operations including creation, updates, and removal.
 * Handles both HikariCP and Tomcat JDBC connection pools based on configuration.
 */
//@Service // Temporarily disabled for testing
public class TenantLifecycleManager {

    private static final Logger logger = LoggerFactory.getLogger(TenantLifecycleManager.class);

    private final MultiTenantConnectionProviderImpl connectionProvider;

    @Autowired
    public TenantLifecycleManager(MultiTenantConnectionProviderImpl connectionProvider) {
        this.connectionProvider = connectionProvider;
    }

    /**
     * Add a tenant DataSource.
     */
    public void addTenant(String tenantKey, DataSource dataSource) {
        if (tenantKey != null && dataSource != null) {
            connectionProvider.addTenant(tenantKey, dataSource);
            logger.info("Added tenant DataSource: {}", tenantKey);
        }
    }

    /**
     * Remove a tenant DataSource.
     */
    public void removeTenant(String tenantKey) {
        if (tenantKey != null) {
            connectionProvider.removeTenant(tenantKey);
            logger.info("Removed tenant DataSource: {}", tenantKey);
        }
    }

    /**
     * Get tenant statistics for monitoring.
     */
    public TenantStats getTenantStats() {
        ConcurrentHashMap<String, DataSource> tenantDataSources = connectionProvider.getTenantDataSources();
        int totalTenants = tenantDataSources.size();
        
        return new TenantStats(totalTenants, totalTenants);
    }

    /**
     * Health check for a specific tenant.
     */
    public boolean isHealthy(String tenantKey) {
        try {
            ConcurrentHashMap<String, DataSource> tenantDataSources = connectionProvider.getTenantDataSources();
            DataSource dataSource = tenantDataSources.get(tenantKey);
            
            if (dataSource == null) {
                return false;
            }
            
            // Test connection
            try (var connection = dataSource.getConnection()) {
                return connection.isValid(5); // 5 second timeout
            }
            
        } catch (Exception e) {
            logger.warn("Health check failed for tenant: {}", tenantKey, e);
            return false;
        }
    }

    /**
     * Statistics holder for tenant information.
     */
    public static class TenantStats {
        private final int totalTenants;
        private final int activeTenants;

        public TenantStats(int totalTenants, int activeTenants) {
            this.totalTenants = totalTenants;
            this.activeTenants = activeTenants;
        }

        public int getTotalTenants() {
            return totalTenants;
        }

        public int getActiveTenants() {
            return activeTenants;
        }

        @Override
        public String toString() {
            return String.format("TenantStats{totalTenants=%d, activeTenants=%d}", 
                               totalTenants, activeTenants);
        }
    }
} 