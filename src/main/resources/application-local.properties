cloud.aws.credentials.profile-name=Administrator-086881067392
cloud.aws.credentials.useDefaultAwsCredentialsChain=true
cloud.aws.region.static=us-east-2
aws.sns-stripe-arn=arn:aws:sns:us-east-1:086881067392:stripe_refunds_topic

spring.config.import=optional:secrets.properties
nymbl.v2.url=http://localhost:4200/

## Both of these need ot be combined and code updated accordingly
base.url=http://localhost:8080

# Cron for ERA download/processing
## Zirmed processes claims 4 times a day 9AM, 12PM, 3PM, and 7PM EST (offset 30 minutes)
nymbl.era.download=0 30 9,12,15,19 * * ?
nymbl.era.process=0 0 10,13,16,20 * * ?

db.url=*************************************************************************************************************************************************************
db.url.cluster=*************************************************************************************************************************************************************
db.url.reader=*************************************************************************************************************************************************************
upload.directory=/opt/nymbl/uploads
empire.client.id=4
#Logging Levels - Coordinate with logback.xml and config beans
logging.level.root=ERROR
#
logging.level.org.springframework=ERROR
#logging.level.org.hibernate=ERROR
logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=ERROR
#logging.level.org.springframework.security=ERROR

# Redis Config
spring.cache.type=redis
spring.data.redis.host=localhost
spring.data.redis.port=6379
#Sentry
#if you enable this (by uncommenting the real DSN below), tag sentry.config.environment (below) with something unique to see YOUR errors:
sentry.config.dsn=
#sentry.config.dsn=https://<EMAIL>/5608486
sentry.config.environment=${user.name}
pspdfkit.license=
#qa.migration.schema=anew_life_po
#spring.flyway.user=root
#spring.flyway.password=
#nymbl.dbUrlPrefix=jdbc:mysql://

spring.cloud.azure.active-directory.enabled=true
spring.cloud.azure.active-directory.profile.tenant-id=organizations

#spring.flyway.url=************************************************************************
spring.cloud.azure.active-directory.credential.client-id=9af20cd2-9a29-4b6e-87bf-2a1d236eb6d0

shortUrl.domain=http://localhost:8080

evaluation.secret.webhook=secret-webhook-parameter
eval.input.bucket=eval-upload-bucket
eval.input.prefix={tenant}/{branch}/therapist-{therapistId}/patient-{patientId}/
evaluation.sqs.queue.url=sqs.us-east-1.localhost.localstack:4566/000000000000/nl2-upload-queue
evaluation.localstack.sqs.queue.url=http://sqs.us-east-1.localhost.localstack.cloud:4566/000000000000/nl2-upload-queue
evaluation.localstack.s3.url=http://s3.localhost.localstack.cloud:4566

lmn.template.file.name=%s-Letter of Medical Necessity Template.pdf
lmn.template.file.template=%s.json
lmn.template.file.description=Nymbl LMN %s Template

nymbl.evaluation.invite=Hello, \n\n\n You have been requested to upload your Evaluation & generate your Letter of Medical Necessity for Patient ID %s - %s by %s. Once you've completed your evaluation for this patient, or once you are with the patient and would like to complete the evaluation in real time, please click the link below to access the portal to upload your evaluation or complete the form within the portal. The link below expires 30 days from the day this email was sent. \n\n\n %s \n\n\n
nymbl.evaluation.subject=New Evaluation/LMN Request for %s
nymbl.evaluation.review=Hello, \n\n\n Your Evaluation has been processed for Patient ID %s and your Letter of Medical Necessity Summary is ready for review.  Please click the link below to access the portal to review and sign your LMN Summary. The link below expires 30 days from the day this email was sent. \n\n\n %s \n\n\n
nymbl.evaluation.subject.review=LMN Summary Ready for Review


analysis.lmn.service=false
eval.token.expiration=330
review.token.expiration=604800

# UI Runtime Config Parameter Store path for local api key
ui.runtime.parameter.path=/config/nymbl_local/ui

# CORS Domains
management.endpoints.web.cors.allowed-origins=http://localhost:8080/,http://localhost:4200/
# AWS Lambda Configuration for LocalStack
aws.lambda.endpoint=http://localhost:4567
lmn.pdf.lambda.function=lmn_pdf_generator-dev-function

# OpenAPI/Swagger Configuration - Explicitly enabled in local environment
swagger.ui.enabled=false
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false

# HikariCP Settings for local testing
# Set to true to test HikariCP locally, false for Tomcat JDBC (default)
nymbl.database.hikari.enabled=false
