package com.nymbl.database;

import com.codahale.metrics.MetricRegistry;
import com.zaxxer.hikari.HikariDataSource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for HikariCP functionality.
 * Tests the ConnectionPoolFactory directly without Spring context.
 */
class HikariIntegrationTest {

    private ConnectionPoolFactory connectionPoolFactory;
    private MetricRegistry metricRegistry;
    private ConnectionPoolFactory.DatabaseConfig testConfig;

    @BeforeEach
    void setUp() {
        // Create a metric registry for testing
        metricRegistry = new MetricRegistry();

        // Create the connection pool factory with the metric registry
        connectionPoolFactory = new ConnectionPoolFactory(metricRegistry);

        // Set HikariCP as enabled via reflection
        try {
            java.lang.reflect.Field hikariEnabledField = ConnectionPoolFactory.class.getDeclaredField("hikariEnabled");
            hikariEnabledField.setAccessible(true);
            hikariEnabledField.set(connectionPoolFactory, true);

            java.lang.reflect.Field poolTypeField = ConnectionPoolFactory.class.getDeclaredField("poolType");
            poolTypeField.setAccessible(true);
            poolTypeField.set(connectionPoolFactory, "hikari");
        } catch (Exception e) {
            fail("Failed to set HikariCP as enabled: " + e.getMessage());
        }

        // Create test database config
        testConfig = new ConnectionPoolFactory.DatabaseConfig("test_db");
        testConfig.setJdbcUrl("jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE");
        testConfig.setUsername("sa");
        testConfig.setPassword("");
        testConfig.setDriverClassName("org.h2.Driver");
        testConfig.setMaxPoolSize(10);
        testConfig.setMinPoolSize(2);
    }

    @Test
    void testHikariEnabled() {
        assertTrue(connectionPoolFactory.isHikariEnabled(),
                   "HikariCP should be enabled in test configuration");
    }

    @Test
    void testCreateHikariDataSource() {
        // Create a HikariCP data source
        DataSource dataSource = connectionPoolFactory.createDataSource(testConfig);

        // Verify it's a HikariDataSource
        assertNotNull(dataSource, "DataSource should not be null");
        assertTrue(dataSource instanceof HikariDataSource,
                   "DataSource should be HikariDataSource, but was: " + dataSource.getClass().getName());
    }

    @Test
    void testHikariConnectionRetrieval() throws SQLException {
        // Create a HikariCP data source
        DataSource dataSource = connectionPoolFactory.createDataSource(testConfig);
        assertNotNull(dataSource, "DataSource should not be null");

        // Test connection retrieval
        try (Connection connection = dataSource.getConnection()) {
            assertNotNull(connection, "Connection should not be null");
            assertFalse(connection.isClosed(), "Connection should be open");
            assertTrue(connection.isValid(5), "Connection should be valid");
        }
    }

    @Test
    void testMultipleConnections() throws SQLException {
        // Create a HikariCP data source
        DataSource dataSource = connectionPoolFactory.createDataSource(testConfig);
        assertNotNull(dataSource, "DataSource should not be null");

        // Test multiple concurrent connections
        try (Connection conn1 = dataSource.getConnection();
             Connection conn2 = dataSource.getConnection()) {

            assertNotNull(conn1, "First connection should not be null");
            assertNotNull(conn2, "Second connection should not be null");
            assertNotSame(conn1, conn2, "Connections should be different instances");
        }
    }

    @Test
    void testMetricsRegistration() {
        // Create a HikariCP data source which should register metrics
        DataSource hikariDs = connectionPoolFactory.createDataSource(testConfig);
        assertNotNull(hikariDs, "HikariCP DataSource should be created successfully");

        // Verify that metrics were registered (at least some metrics should be present)
        assertFalse(metricRegistry.getMetrics().isEmpty(),
                   "Metrics should be registered when HikariCP is used");

        // Verify that metrics contain HikariCP-specific metrics
        boolean hasHikariMetrics = metricRegistry.getMetrics().keySet().stream()
                .anyMatch(name -> name.contains("hikari") || name.contains("pool"));
        assertTrue(hasHikariMetrics, "HikariCP-specific metrics should be registered");
    }

    @Test
    void testConnectionPoolSizeLimits() throws SQLException {
        // Configure a small pool for testing
        testConfig.setMaxPoolSize(3);
        testConfig.setMinPoolSize(1);

        // Create the data source
        HikariDataSource hikariDs = (HikariDataSource) connectionPoolFactory.createDataSource(testConfig);
        assertNotNull(hikariDs, "HikariCP DataSource should be created successfully");

        // Verify pool size configuration
        assertEquals(3, hikariDs.getMaximumPoolSize(), "Maximum pool size should match configuration");
        assertEquals(1, hikariDs.getMinimumIdle(), "Minimum idle connections should match configuration");

        // Get connections up to the maximum
        Connection conn1 = hikariDs.getConnection();
        Connection conn2 = hikariDs.getConnection();
        Connection conn3 = hikariDs.getConnection();

        assertNotNull(conn1, "First connection should not be null");
        assertNotNull(conn2, "Second connection should not be null");
        assertNotNull(conn3, "Third connection should not be null");

        // Close connections to avoid leaks
        conn1.close();
        conn2.close();
        conn3.close();

        // Clean up
        hikariDs.close();
    }

    @Test
    void testConnectionValidation() throws SQLException {
        // Configure validation settings
        testConfig.setValidationQuery("SELECT 1");
        testConfig.setValidationTimeout(1000); // 1 second

        // Create the data source
        HikariDataSource hikariDs = (HikariDataSource) connectionPoolFactory.createDataSource(testConfig);
        assertNotNull(hikariDs, "HikariCP DataSource should be created successfully");

        // Get a connection
        Connection connection = hikariDs.getConnection();
        assertNotNull(connection, "Connection should not be null");

        // Verify connection is valid
        assertTrue(connection.isValid(1), "Connection should be valid");

        // Close connection
        connection.close();

        // Clean up
        hikariDs.close();
    }

    @Test
    void testPoolShutdown() throws SQLException {
        // Create the data source
        HikariDataSource hikariDs = (HikariDataSource) connectionPoolFactory.createDataSource(testConfig);
        assertNotNull(hikariDs, "HikariCP DataSource should be created successfully");

        // Get a connection
        Connection connection = hikariDs.getConnection();
        assertNotNull(connection, "Connection should not be null");

        // Close connection
        connection.close();

        // Shutdown the pool
        hikariDs.close();

        // Verify pool is closed
        assertTrue(hikariDs.isClosed(), "HikariCP pool should be closed after shutdown");

        // Verify we can't get connections after shutdown
        Exception exception = assertThrows(SQLException.class, () -> {
            hikariDs.getConnection();
        });

        assertTrue(exception.getMessage().contains("closed") ||
                   exception.getMessage().contains("shutdown"),
                  "Exception should indicate pool is closed: " + exception.getMessage());
    }

    @Test
    void testConnectionTimeout() throws SQLException {
        // Configure a very small pool with short timeout
        testConfig.setMaxPoolSize(1);
        testConfig.setConnectionTimeout(500); // 500ms timeout

        // Create the data source
        HikariDataSource hikariDs = (HikariDataSource) connectionPoolFactory.createDataSource(testConfig);
        assertNotNull(hikariDs, "HikariCP DataSource should be created successfully");

        // Get the only connection
        Connection connection = hikariDs.getConnection();
        assertNotNull(connection, "Connection should not be null");

        // Try to get another connection - should timeout
        Exception exception = assertThrows(SQLException.class, () -> {
            hikariDs.getConnection();
        });

        // Print the actual error message for debugging
        System.out.println("Actual timeout error message: " + exception.getMessage());

        // HikariCP error message format: "Connection is not available, request timed out after XXXms."
        assertTrue(exception.getMessage().contains("Connection is not available") ||
                   exception.getMessage().contains("request timed out"),
                  "Exception should indicate connection timeout: " + exception.getMessage());

        // Close connection
        connection.close();

        // Clean up
        hikariDs.close();
    }
    @Test
    void testIdleTimeout() throws SQLException, InterruptedException {
        // Configure a pool with short idle timeout
        testConfig.setMaxPoolSize(5);
        testConfig.setMinPoolSize(1);
        testConfig.setIdleTimeout(1000); // 1 second idle timeout

        // Create the data source
        HikariDataSource hikariDs = (HikariDataSource) connectionPoolFactory.createDataSource(testConfig);
        assertNotNull(hikariDs, "HikariCP DataSource should be created successfully");

        // Get and immediately close multiple connections to create idle connections
        for (int i = 0; i < 3; i++) {
            Connection conn = hikariDs.getConnection();
            conn.close();
        }

        // Wait for idle timeout to expire
        Thread.sleep(2000);

        // Verify idle connections were removed (should be close to minPoolSize)
        // Note: This is an approximate test as we can't directly check pool size
        int idleConnections = hikariDs.getHikariPoolMXBean().getIdleConnections();

        // Log pool state for debugging
        System.out.println("Idle connections: " + idleConnections);
        System.out.println("Active connections: " + hikariDs.getHikariPoolMXBean().getActiveConnections());
        System.out.println("Total connections: " + hikariDs.getHikariPoolMXBean().getTotalConnections());

        assertTrue(idleConnections <= 2,
                  "Idle connections should be reduced after timeout, but was: " + idleConnections);

        // Clean up
        hikariDs.close();
    }

    @Test
    void testMaxLifetime() throws SQLException, InterruptedException {
        // Configure a pool with short max lifetime
        testConfig.setMaxPoolSize(3);
        testConfig.setMinPoolSize(1);
        testConfig.setMaxLifetime(2000); // 2 second max lifetime

        // Create the data source
        HikariDataSource hikariDs = (HikariDataSource) connectionPoolFactory.createDataSource(testConfig);
        assertNotNull(hikariDs, "HikariCP DataSource should be created successfully");

        // Get and keep a connection
        Connection conn = hikariDs.getConnection();
        assertNotNull(conn, "Connection should not be null");

        // Wait for max lifetime to expire
        Thread.sleep(3000);

        // Connection should still be usable (HikariCP handles recycling internally)
        assertTrue(conn.isValid(1), "Connection should still be valid after max lifetime");

        // Close connection
        conn.close();

        // Clean up
        hikariDs.close();
    }

    @Test
    void testLeakDetection() throws SQLException, InterruptedException {
        // Configure a pool with leak detection
        testConfig.setMaxPoolSize(2);
        testConfig.setMinPoolSize(1);
        testConfig.setLeakDetectionThreshold(1000); // 1 second leak detection

        // Create the data source
        HikariDataSource hikariDs = (HikariDataSource) connectionPoolFactory.createDataSource(testConfig);
        assertNotNull(hikariDs, "HikariCP DataSource should be created successfully");

        // Get a connection but don't close it (simulate a leak)
        Connection conn = hikariDs.getConnection();
        assertNotNull(conn, "Connection should not be null");

        // Wait for leak detection to trigger
        Thread.sleep(2000);

        // We can't directly test leak detection, but we can verify the connection is still valid
        assertTrue(conn.isValid(1), "Connection should still be valid");

        // Close connection to avoid actual leak
        conn.close();

        // Clean up
        hikariDs.close();
    }

    @Test
    void testDataSourceProperties() {
        // Configure data source properties
        testConfig.setMaxPoolSize(5);
        testConfig.setMinPoolSize(2);

        // Create the data source
        HikariDataSource hikariDs = (HikariDataSource) connectionPoolFactory.createDataSource(testConfig);
        assertNotNull(hikariDs, "HikariCP DataSource should be created successfully");

        // Verify data source properties were set
        assertEquals("test_db-pool", hikariDs.getPoolName(), "Pool name should match configuration");
        assertEquals(5, hikariDs.getMaximumPoolSize(), "Maximum pool size should match configuration");
        assertEquals(2, hikariDs.getMinimumIdle(), "Minimum idle connections should match configuration");
        assertEquals("SELECT 1", hikariDs.getConnectionTestQuery(), "Connection test query should match configuration");

        // Clean up
        hikariDs.close();
    }

    @Test
    void testConcurrentTenantAddition() throws InterruptedException {
        // Test concurrent tenant addition to verify thread safety
        int threadCount = 5;
        Thread[] threads = new Thread[threadCount];

        for (int i = 0; i < threadCount; i++) {
            final int tenantId = i;
            threads[i] = new Thread(() -> {
                ConnectionPoolFactory.DatabaseConfig config = new ConnectionPoolFactory.DatabaseConfig("tenant_" + tenantId);
                config.setJdbcUrl("jdbc:h2:mem:tenant" + tenantId + ";DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE");
                config.setUsername("sa");
                config.setPassword("");
                config.setDriverClassName("org.h2.Driver");

                DataSource dataSource = connectionPoolFactory.createDataSource(config);
                assertNotNull(dataSource, "DataSource should be created for tenant " + tenantId);
            });
        }

        // Start all threads
        for (Thread thread : threads) {
            thread.start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            thread.join();
        }
    }

    @Test
    void testResourceCleanupVerification() {
        DataSource rawDataSource = connectionPoolFactory.createDataSource(testConfig);
        CompatibleDataSource compatibleDataSource = new CompatibleDataSource(
            rawDataSource, "test_db", "hikari");

        assertFalse(compatibleDataSource.isClosed(), "DataSource should not be closed initially");

        // Close and verify cleanup
        compatibleDataSource.close();
        assertTrue(compatibleDataSource.isClosed(), "DataSource should be closed after close()");

        // Verify that getting connection after close throws exception
        assertThrows(Exception.class, compatibleDataSource::getConnection,
                     "Should throw exception when getting connection from closed DataSource");
    }

    @Test
    void testConnectionValidationFailure() throws Exception {
        DataSource rawDataSource = connectionPoolFactory.createDataSource(testConfig);
        CompatibleDataSource compatibleDataSource = new CompatibleDataSource(
            rawDataSource, "test_db", "hikari");

        // Test that connection validation works
        try (var connection = compatibleDataSource.getConnection()) {
            assertNotNull(connection, "Connection should not be null");
            assertTrue(connection.isValid(5), "Connection should be valid");
        }
    }

    @Test
    void testHealthCheck() {
        DataSource rawDataSource = connectionPoolFactory.createDataSource(testConfig);
        CompatibleDataSource compatibleDataSource = new CompatibleDataSource(
            rawDataSource, "test_db", "hikari");

        // Test that health check works when DataSource is open
        assertTrue(compatibleDataSource.isHealthy(), "DataSource should be healthy when open");

        // Test that health check fails when DataSource is closed
        compatibleDataSource.close();
        assertFalse(compatibleDataSource.isHealthy(), "DataSource should not be healthy when closed");
    }
}