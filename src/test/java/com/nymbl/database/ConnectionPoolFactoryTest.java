package com.nymbl.database;

import com.codahale.metrics.MetricRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import javax.sql.DataSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ConnectionPoolFactory.
 */
@ExtendWith(MockitoExtension.class)
class ConnectionPoolFactoryTest {

    @Mock
    private MetricRegistry metricRegistry;

    private ConnectionPoolFactory connectionPoolFactory;
    private ConnectionPoolFactory.DatabaseConfig testConfig;

    @BeforeEach
    void setUp() {
        connectionPoolFactory = new ConnectionPoolFactory(metricRegistry);

        // Set up test configuration
        testConfig = new ConnectionPoolFactory.DatabaseConfig("test_db");
        testConfig.setJdbcUrl("jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE");
        testConfig.setUsername("sa");
        testConfig.setPassword("");
        testConfig.setDriverClassName("org.h2.Driver");
        testConfig.setMaxPoolSize(10);
        testConfig.setMinPoolSize(2);
    }

    @Test
    void testHikariDisabledByDefault() {
        assertFalse(connectionPoolFactory.isHikariEnabled(),
                   "HikariCP should be disabled by default");
    }

    @Test
    void testCreateDataSourceWithHikariDisabled() {
        // Set HikariCP disabled
        ReflectionTestUtils.setField(connectionPoolFactory, "hikariEnabled", false);
        ReflectionTestUtils.setField(connectionPoolFactory, "poolType", "tomcat");

        DataSource dataSource = connectionPoolFactory.createDataSource(testConfig);

        assertNotNull(dataSource, "DataSource should not be null");
        assertTrue(dataSource instanceof org.apache.tomcat.jdbc.pool.DataSource,
                   "Should create Tomcat JDBC DataSource when HikariCP is disabled");
    }

    @Test
    void testCreateDataSourceWithHikariEnabled() {
        // Set HikariCP enabled
        ReflectionTestUtils.setField(connectionPoolFactory, "hikariEnabled", true);
        ReflectionTestUtils.setField(connectionPoolFactory, "poolType", "hikari");
        ReflectionTestUtils.setField(connectionPoolFactory, "prepStmtCacheSize", 250);
        ReflectionTestUtils.setField(connectionPoolFactory, "prepStmtCacheSqlLimit", 2048);
        ReflectionTestUtils.setField(connectionPoolFactory, "allowFallback", false);

        DataSource dataSource = connectionPoolFactory.createDataSource(testConfig);

        assertNotNull(dataSource, "DataSource should not be null");
        assertTrue(dataSource instanceof com.zaxxer.hikari.HikariDataSource,
                   "Should create HikariCP DataSource when HikariCP is enabled");
    }

    @Test
    void testFallbackConfigurationProperty() {
        // Test that the allowFallback property is properly configured
        ReflectionTestUtils.setField(connectionPoolFactory, "allowFallback", true);

        // Verify the property was set (this tests the configuration mechanism)
        Boolean allowFallback = (Boolean) ReflectionTestUtils.getField(connectionPoolFactory, "allowFallback");
        assertNotNull(allowFallback, "allowFallback property should not be null");
        assertTrue(allowFallback, "allowFallback property should be configurable");

        // Test with fallback disabled
        ReflectionTestUtils.setField(connectionPoolFactory, "allowFallback", false);
        allowFallback = (Boolean) ReflectionTestUtils.getField(connectionPoolFactory, "allowFallback");
        assertNotNull(allowFallback, "allowFallback property should not be null");
        assertFalse(allowFallback, "allowFallback property should be configurable to false");
    }

    @Test
    void testDatabaseConfigDefaults() {
        ConnectionPoolFactory.DatabaseConfig config = new ConnectionPoolFactory.DatabaseConfig("test");

        assertEquals("test", config.getDatabaseName());
        assertEquals(20, config.getMaxPoolSize());
        assertEquals(5, config.getMinPoolSize());
        assertEquals("SELECT 1", config.getValidationQuery());
        assertEquals(5000, config.getValidationTimeout());
        assertEquals(30000, config.getConnectionTimeout());
        assertEquals(600000, config.getIdleTimeout());
        assertEquals(1800000, config.getMaxLifetime());
        assertEquals(60000, config.getLeakDetectionThreshold());
        assertEquals("test-pool", config.getPoolName());
    }

    @Test
    void testConfigurableProperties() {
        // Test that configurable properties are used
        ReflectionTestUtils.setField(connectionPoolFactory, "prepStmtCacheSize", 500);
        ReflectionTestUtils.setField(connectionPoolFactory, "prepStmtCacheSqlLimit", 4096);

        // These values should be used when creating HikariCP DataSource
        // This is more of an integration test, but validates the configuration flow
        assertNotNull(connectionPoolFactory);
    }

    @Test
    void testPoolTypeConfiguration() {
        // Test pool type configuration
        ReflectionTestUtils.setField(connectionPoolFactory, "poolType", "hikari");
        ReflectionTestUtils.setField(connectionPoolFactory, "hikariEnabled", false);
        ReflectionTestUtils.setField(connectionPoolFactory, "prepStmtCacheSize", 250);
        ReflectionTestUtils.setField(connectionPoolFactory, "prepStmtCacheSqlLimit", 2048);
        ReflectionTestUtils.setField(connectionPoolFactory, "allowFallback", false);

        // Should still create HikariCP when poolType is "hikari" even if hikariEnabled is false
        DataSource dataSource = connectionPoolFactory.createDataSource(testConfig);

        assertNotNull(dataSource);
        assertTrue(dataSource instanceof com.zaxxer.hikari.HikariDataSource,
                   "Should create HikariCP DataSource when poolType is 'hikari'");
    }
}
