target
.idea
.cursor
coverage
node_modules
npm-debug.log
*.iml
src/js/dist/*
**/*.sqlite3
*.log
.DS_Store
.project
.classpath
.settings
.sass-cache
.tmp
.factorypath
# Nymbl system logs
nymbl_log*
com.nymbl.component.patient.model.Patient
bower_components
src/main/resources/static/*
app/views/tmpl/common/*
.vscode/*
package-lock.json
# Elastic Beanstalk Files
.elasticbeanstalk/*
!.elasticbeanstalk/*.cfg.yml
!.elasticbeanstalk/*.global.yml
# Docker data
redis-data
/.jpb/
/mssql/docker/db/
/postgres/docker/db

nymbl-main.iws
nymbl-main.ipr
nymbl-main.iml

secrets.properties
/mysql8_docker/reader/data/
/mysql8_docker/writer/data/

npm-shrinkwrap.json
.cursor
