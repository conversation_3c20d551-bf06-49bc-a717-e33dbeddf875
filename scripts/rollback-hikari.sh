#!/bin/bash

# HikariCP Rollback Script
# This script provides emergency rollback procedures for HikariCP implementation

set -e

echo "=== HikariCP Rollback Script ==="
echo "This script will help you rollback HikariCP implementation safely"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "SUCCESS" ]; then
        echo -e "${GREEN}[SUCCESS]${NC} $message"
    elif [ "$status" = "ERROR" ]; then
        echo -e "${RED}[ERROR]${NC} $message"
    elif [ "$status" = "WARNING" ]; then
        echo -e "${YELLOW}[WARNING]${NC} $message"
    elif [ "$status" = "INFO" ]; then
        echo -e "${BLUE}[INFO]${NC} $message"
    fi
}

# Function to prompt for confirmation
confirm() {
    local message=$1
    read -p "$message (y/N): " response
    case $response in
        [yY][eE][sS]|[yY]) return 0 ;;
        *) return 1 ;;
    esac
}

# Function to backup a file
backup_file() {
    local file=$1
    if [ -f "$file" ]; then
        local backup_file="${file}.rollback-backup.$(date +%Y%m%d_%H%M%S)"
        cp "$file" "$backup_file"
        print_status "INFO" "Backed up $file to $backup_file"
    fi
}

echo ""
print_status "WARNING" "IMPORTANT: This script will rollback HikariCP changes."
print_status "WARNING" "Make sure to stop the application before proceeding."
echo ""

if ! confirm "Do you want to proceed with the rollback?"; then
    print_status "INFO" "Rollback cancelled by user"
    exit 0
fi

echo ""
echo "=== ROLLBACK PROCEDURES ==="

echo ""
echo "1. Quick Emergency Rollback (Feature Flag)"
echo "   - Fastest way to disable HikariCP and revert to Tomcat JDBC"

if confirm "Do you want to perform quick emergency rollback?"; then
    print_status "INFO" "Performing quick emergency rollback..."
    
    # Backup current application.properties
    backup_file "src/main/resources/application.properties"
    
    # Disable HikariCP feature flag
    if [ -f "src/main/resources/application.properties" ]; then
        if grep -q "nymbl.database.hikari.enabled" src/main/resources/application.properties; then
            sed -i.bak 's/nymbl.database.hikari.enabled=true/nymbl.database.hikari.enabled=false/' src/main/resources/application.properties
            print_status "SUCCESS" "Disabled HikariCP feature flag"
        else
            echo "nymbl.database.hikari.enabled=false" >> src/main/resources/application.properties
            print_status "SUCCESS" "Added HikariCP feature flag (disabled)"
        fi
    fi
    
    print_status "SUCCESS" "Quick emergency rollback completed!"
    print_status "INFO" "Restart the application to use Tomcat JDBC"
    echo ""
fi

echo ""
echo "2. Full Rollback (Remove HikariCP Implementation)"
echo "   - Removes HikariCP code while preserving functionality"

if confirm "Do you want to perform full rollback (remove HikariCP code)?"; then
    print_status "WARNING" "This will remove HikariCP implementation files"
    
    if confirm "Are you sure you want to continue with full rollback?"; then
        print_status "INFO" "Performing full rollback..."
        
        # Files to remove (HikariCP-specific implementations)
        FILES_TO_REMOVE=(
            "src/main/java/com/nymbl/database/TenantLifecycleManager.java"
            "src/main/java/com/nymbl/database/DatabaseMonitoringService.java"
            "src/test/java/com/nymbl/database/HikariIntegrationTest.java"
            "src/main/resources/application-hikari.properties"
            "src/main/resources/application-production.properties"
        )
        
        # Backup and remove HikariCP-specific files
        for file in "${FILES_TO_REMOVE[@]}"; do
            if [ -f "$file" ]; then
                backup_file "$file"
                rm "$file"
                print_status "SUCCESS" "Removed $file"
            else
                print_status "WARNING" "$file not found (may already be removed)"
            fi
        done
        
        # Revert core files to Tomcat-only implementation
        print_status "INFO" "Reverting core files to Tomcat-only implementation..."
        
        # Backup and modify ConnectionPoolFactory
        if [ -f "src/main/java/com/nymbl/database/ConnectionPoolFactory.java" ]; then
            backup_file "src/main/java/com/nymbl/database/ConnectionPoolFactory.java"
            print_status "INFO" "Manual step required: Revert ConnectionPoolFactory.java to Tomcat-only implementation"
        fi
        
        # Backup and modify CompatibleDataSource
        if [ -f "src/main/java/com/nymbl/database/CompatibleDataSource.java" ]; then
            backup_file "src/main/java/com/nymbl/database/CompatibleDataSource.java"
            print_status "INFO" "Manual step required: Revert CompatibleDataSource.java to Tomcat-only implementation"
        fi
        
        # Backup and modify BatchDataSourceProvider
        if [ -f "src/main/java/com/nymbl/database/BatchDataSourceProvider.java" ]; then
            backup_file "src/main/java/com/nymbl/database/BatchDataSourceProvider.java"
            print_status "INFO" "Manual step required: Revert BatchDataSourceProvider.java to original implementation"
        fi
        
        print_status "SUCCESS" "Full rollback completed!"
        print_status "WARNING" "Manual code review and modification required for complete rollback"
        echo ""
    fi
fi

echo ""
echo "3. Maven Dependencies Rollback"
echo "   - Removes HikariCP dependencies from pom.xml"

if confirm "Do you want to rollback Maven dependencies?"; then
    print_status "INFO" "Rolling back Maven dependencies..."
    
    # Backup pom.xml
    backup_file "pom.xml"
    
    # Remove HikariCP dependency (add exclusion back)
    if grep -q "zaxxer.*hikari" pom.xml; then
        print_status "INFO" "Manual step required: Remove HikariCP dependency from pom.xml"
        print_status "INFO" "Manual step required: Add HikariCP exclusion back to spring-boot-starter-data-jpa"
    fi
    
    # Remove metrics dependencies if not used elsewhere
    print_status "INFO" "Manual step required: Review and remove Dropwizard metrics dependencies if not needed"
    
    print_status "SUCCESS" "Dependencies rollback guidance provided"
    echo ""
fi

echo ""
echo "4. Configuration Rollback"
echo "   - Removes HikariCP configuration properties"

if confirm "Do you want to rollback configuration properties?"; then
    print_status "INFO" "Rolling back configuration properties..."
    
    # Remove HikariCP properties from application.properties
    if [ -f "src/main/resources/application.properties" ]; then
        backup_file "src/main/resources/application.properties"
        
        # Remove HikariCP-related properties
        sed -i.bak '/nymbl\.database\.hikari\./d' src/main/resources/application.properties
        sed -i.bak '/nymbl\.database\.pool\.type/d' src/main/resources/application.properties
        
        print_status "SUCCESS" "Removed HikariCP configuration properties"
    fi
    
    print_status "SUCCESS" "Configuration rollback completed"
    echo ""
fi

echo ""
echo "=== POST-ROLLBACK VERIFICATION ==="

# Verify that Tomcat JDBC is still available
if grep -q "tomcat-jdbc" pom.xml; then
    print_status "SUCCESS" "Tomcat JDBC dependency is present"
else
    print_status "ERROR" "Tomcat JDBC dependency not found - you may need to add it back"
fi

# Check compilation
print_status "INFO" "Checking project compilation..."
if mvn compile -q > /dev/null 2>&1; then
    print_status "SUCCESS" "Project compiles successfully after rollback"
else
    print_status "ERROR" "Project compilation failed after rollback"
    print_status "INFO" "Run 'mvn compile' to see detailed compilation errors"
fi

echo ""
echo "=== ROLLBACK SUMMARY ==="
print_status "INFO" "Rollback procedures completed"
print_status "INFO" "Backup files created with .rollback-backup.* extension"

echo ""
echo "NEXT STEPS:"
echo "1. Review and test the application thoroughly"
echo "2. If compilation fails, check backup files and restore manually"
echo "3. Start the application and verify it works with Tomcat JDBC"
echo "4. Monitor application logs for any issues"
echo "5. Clean up backup files once verification is complete"

echo ""
echo "EMERGENCY CONTACTS:"
echo "- If issues persist, contact the development team immediately"
echo "- Keep backup files until rollback is fully verified"
echo "- Document any issues encountered during rollback"

echo ""
print_status "SUCCESS" "Rollback script completed" 